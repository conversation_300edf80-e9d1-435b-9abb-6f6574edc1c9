import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class WriterController extends GetxController {
  static WriterController get instance => Get.find();



  RxList<String> stringList = List.generate(108, (index) => '').obs;
  // Color properties
  Rx<Color> selectedTextColor = Colors.black.obs;
  Rx<Color> selectedCellColor = Colors.grey[100]!.obs;

  int currentIndex = 0;

  // Progress variables
  RxDouble progressValue = 0.0.obs;
  RxInt writeCount = 0.obs;

  void addToStringList() {
    if (currentIndex < stringList.length) {
      stringList[currentIndex] = 'RAM';
      currentIndex++;
      writeCount.value++;
      progressValue.value = currentIndex / stringList.length;
    }
  }
  // Function to change text color
  void changeTextColor(Color color) {
    selectedTextColor.value = color;
    update(); // Notify UI
  }

  // Function to change cell color
  void changeCellColor(Color color) {
    selectedCellColor.value = color;
    update(); // Notify UI
  }
}
