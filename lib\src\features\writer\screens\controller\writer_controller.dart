import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:login_flutter_app/src/services/firebase_service.dart';
import 'package:login_flutter_app/src/features/core/controllers/language_controller.dart';

class WriterController extends GetxController {
  static WriterController get instance => Get.find();

  RxList<String> stringList = List.generate(108, (index) => '').obs;
  // Color properties
  Rx<Color> selectedTextColor = Colors.black.obs;
  Rx<Color> selectedCellColor = Colors.grey[100]!.obs;

  int currentIndex = 0;

  // Progress variables
  RxDouble progressValue = 0.0.obs;
  RxInt writeCount = 0.obs;

  // Pagination variables
  RxInt currentPage = 1.obs;
  RxInt totalPagesCompletedToday = 0.obs;
  RxBool isPageCompleted = false.obs;

  // Session tracking
  DateTime? sessionStartTime;

  // Storage for user preferences
  final GetStorage _storage = GetStorage();

  @override
  void onInit() {
    super.onInit();
    sessionStartTime = DateTime.now();
    _loadColorPreferences();
    _loadDailyProgress();
  }

  // Load saved color preferences
  void _loadColorPreferences() {
    // Load text color
    final textColorValue = _storage.read('ram_text_color');
    if (textColorValue != null) {
      selectedTextColor.value = Color(textColorValue);
    }

    // Load cell color
    final cellColorValue = _storage.read('ram_cell_color');
    if (cellColorValue != null) {
      selectedCellColor.value = Color(cellColorValue);
    }
  }

  // Save color preferences
  void _saveColorPreferences() {
    _storage.write('ram_text_color', selectedTextColor.value.value);
    _storage.write('ram_cell_color', selectedCellColor.value.value);
  }

  // Load daily progress
  void _loadDailyProgress() {
    final today = DateTime.now();
    final todayKey = '${today.year}-${today.month}-${today.day}';

    // Load today's completed pages
    final completedToday = _storage.read('completed_pages_$todayKey') ?? 0;
    totalPagesCompletedToday.value = completedToday;

    // Load current page number
    final savedCurrentPage = _storage.read('current_page_$todayKey') ?? 1;
    currentPage.value = savedCurrentPage;
  }

  // Save daily progress
  void _saveDailyProgress() {
    final today = DateTime.now();
    final todayKey = '${today.year}-${today.month}-${today.day}';

    _storage.write('completed_pages_$todayKey', totalPagesCompletedToday.value);
    _storage.write('current_page_$todayKey', currentPage.value);
  }

  void addToStringList() {
    if (currentIndex < stringList.length) {
      // Get the current language and use appropriate text
      final languageController = Get.find<LanguageController>();
      final ramText =
          languageController.currentLanguage.code == 'hi' ? 'राम' : 'RAM';

      stringList[currentIndex] = ramText;
      currentIndex++;
      writeCount.value++;
      progressValue.value = currentIndex / stringList.length;

      // Log Ram Naam Jaap action to Firebase Analytics
      if (Get.isRegistered<FirebaseService>()) {
        FirebaseService.instance.logEvent(
          name: 'ram_naam_written',
          parameters: {
            'count': writeCount.value,
            'progress_percentage': (progressValue.value * 100).round(),
            'session_duration_seconds': sessionStartTime != null
                ? DateTime.now().difference(sessionStartTime!).inSeconds
                : 0,
          },
        );

        // Log milestone events
        if (writeCount.value == 27) {
          // 25% complete
          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '25_percent',
              'count': writeCount.value,
            },
          );
        } else if (writeCount.value == 54) {
          // 50% complete
          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '50_percent',
              'count': writeCount.value,
            },
          );
        } else if (writeCount.value == 81) {
          // 75% complete
          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '75_percent',
              'count': writeCount.value,
            },
          );
        } else if (writeCount.value == 108) {
          // 100% complete - Page completed
          final sessionDuration = sessionStartTime != null
              ? DateTime.now().difference(sessionStartTime!).inSeconds
              : 0;

          FirebaseService.instance.logRamNaamJaapSession(
            boxesFilled: writeCount.value,
            totalBoxes: 108,
            sessionDuration: sessionDuration,
            completionStatus: 'completed',
          );

          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '100_percent_completed',
              'count': writeCount.value,
              'session_duration_seconds': sessionDuration,
            },
          );

          // Complete current page and prepare for next
          _completeCurrentPage();
        }
      }
    }
  }

  // Complete current page and move to next
  void _completeCurrentPage() {
    isPageCompleted.value = true;
    totalPagesCompletedToday.value++;
    _saveDailyProgress();

    // Log page completion to Firebase
    if (Get.isRegistered<FirebaseService>()) {
      FirebaseService.instance.logEvent(
        name: 'ram_naam_page_completed',
        parameters: {
          'page_number': currentPage.value,
          'total_pages_today': totalPagesCompletedToday.value,
          'session_duration_minutes': sessionStartTime != null
              ? DateTime.now().difference(sessionStartTime!).inMinutes
              : 0,
        },
      );
    }

    // Auto-move to next page after a brief delay
    Future.delayed(const Duration(seconds: 2), () {
      moveToNextPage();
    });
  }

  // Move to next page
  void moveToNextPage() {
    currentPage.value++;
    currentIndex = 0;
    writeCount.value = 0;
    progressValue.value = 0.0;
    isPageCompleted.value = false;

    // Reset the grid
    stringList.value = List.generate(108, (index) => '');

    _saveDailyProgress();
    update();

    // Log new page start
    if (Get.isRegistered<FirebaseService>()) {
      FirebaseService.instance.logEvent(
        name: 'ram_naam_page_started',
        parameters: {
          'page_number': currentPage.value,
          'total_pages_today': totalPagesCompletedToday.value,
        },
      );
    }
  }

  // Method to reset scroll position (called from UI)
  void resetScrollPosition() {
    // This will be called from the UI component that has access to the scroll controller
    // The UI will listen to page changes and reset scroll position
  }

  // Reset daily progress (for testing or new day)
  void resetDailyProgress() {
    currentPage.value = 1;
    totalPagesCompletedToday.value = 0;
    currentIndex = 0;
    writeCount.value = 0;
    progressValue.value = 0.0;
    isPageCompleted.value = false;
    stringList.value = List.generate(108, (index) => '');
    _saveDailyProgress();
    update();
  }

  // Function to change text color
  void changeTextColor(Color color) {
    selectedTextColor.value = color;
    _saveColorPreferences(); // Save to storage
    update(); // Notify UI

    // Log color customization to Firebase Analytics
    if (Get.isRegistered<FirebaseService>()) {
      FirebaseService.instance.logEvent(
        name: 'ram_text_color_changed',
        parameters: {
          'color_value': color.value.toString(),
        },
      );
    }
  }

  // Function to change cell color
  void changeCellColor(Color color) {
    selectedCellColor.value = color;
    _saveColorPreferences(); // Save to storage
    update(); // Notify UI

    // Log color customization to Firebase Analytics
    if (Get.isRegistered<FirebaseService>()) {
      FirebaseService.instance.logEvent(
        name: 'ram_cell_color_changed',
        parameters: {
          'color_value': color.value.toString(),
        },
      );
    }
  }
}
