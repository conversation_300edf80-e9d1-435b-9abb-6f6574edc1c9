import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:login_flutter_app/src/services/firebase_service.dart';
import 'package:login_flutter_app/src/features/core/controllers/language_controller.dart';

class WriterController extends GetxController {
  static WriterController get instance => Get.find();

  RxList<String> stringList = List.generate(108, (index) => '').obs;
  // Color properties
  Rx<Color> selectedTextColor = Colors.black.obs;
  Rx<Color> selectedCellColor = Colors.grey[100]!.obs;

  int currentIndex = 0;

  // Progress variables
  RxDouble progressValue = 0.0.obs;
  RxInt writeCount = 0.obs;

  // Session tracking
  DateTime? sessionStartTime;

  // Storage for user preferences
  final GetStorage _storage = GetStorage();

  @override
  void onInit() {
    super.onInit();
    sessionStartTime = DateTime.now();
    _loadColorPreferences();
  }

  // Load saved color preferences
  void _loadColorPreferences() {
    // Load text color
    final textColorValue = _storage.read('ram_text_color');
    if (textColorValue != null) {
      selectedTextColor.value = Color(textColorValue);
    }

    // Load cell color
    final cellColorValue = _storage.read('ram_cell_color');
    if (cellColorValue != null) {
      selectedCellColor.value = Color(cellColorValue);
    }
  }

  // Save color preferences
  void _saveColorPreferences() {
    _storage.write('ram_text_color', selectedTextColor.value.value);
    _storage.write('ram_cell_color', selectedCellColor.value.value);
  }

  void addToStringList() {
    if (currentIndex < stringList.length) {
      // Get the current language and use appropriate text
      final languageController = Get.find<LanguageController>();
      final ramText =
          languageController.currentLanguage.code == 'hi' ? 'राम' : 'RAM';

      stringList[currentIndex] = ramText;
      currentIndex++;
      writeCount.value++;
      progressValue.value = currentIndex / stringList.length;

      // Log Ram Naam Jaap action to Firebase Analytics
      if (Get.isRegistered<FirebaseService>()) {
        FirebaseService.instance.logEvent(
          name: 'ram_naam_written',
          parameters: {
            'count': writeCount.value,
            'progress_percentage': (progressValue.value * 100).round(),
            'session_duration_seconds': sessionStartTime != null
                ? DateTime.now().difference(sessionStartTime!).inSeconds
                : 0,
          },
        );

        // Log milestone events
        if (writeCount.value == 27) {
          // 25% complete
          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '25_percent',
              'count': writeCount.value,
            },
          );
        } else if (writeCount.value == 54) {
          // 50% complete
          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '50_percent',
              'count': writeCount.value,
            },
          );
        } else if (writeCount.value == 81) {
          // 75% complete
          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '75_percent',
              'count': writeCount.value,
            },
          );
        } else if (writeCount.value == 108) {
          // 100% complete
          final sessionDuration = sessionStartTime != null
              ? DateTime.now().difference(sessionStartTime!).inSeconds
              : 0;

          FirebaseService.instance.logRamNaamJaapSession(
            boxesFilled: writeCount.value,
            totalBoxes: 108,
            sessionDuration: sessionDuration,
            completionStatus: 'completed',
          );

          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '100_percent_completed',
              'count': writeCount.value,
              'session_duration_seconds': sessionDuration,
            },
          );
        }
      }
    }
  }

  // Function to change text color
  void changeTextColor(Color color) {
    selectedTextColor.value = color;
    _saveColorPreferences(); // Save to storage
    update(); // Notify UI

    // Log color customization to Firebase Analytics
    if (Get.isRegistered<FirebaseService>()) {
      FirebaseService.instance.logEvent(
        name: 'ram_text_color_changed',
        parameters: {
          'color_value': color.value.toString(),
        },
      );
    }
  }

  // Function to change cell color
  void changeCellColor(Color color) {
    selectedCellColor.value = color;
    _saveColorPreferences(); // Save to storage
    update(); // Notify UI

    // Log color customization to Firebase Analytics
    if (Get.isRegistered<FirebaseService>()) {
      FirebaseService.instance.logEvent(
        name: 'ram_cell_color_changed',
        parameters: {
          'color_value': color.value.toString(),
        },
      );
    }
  }
}
