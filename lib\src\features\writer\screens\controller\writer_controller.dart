import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:login_flutter_app/src/services/firebase_service.dart';

class WriterController extends GetxController {
  static WriterController get instance => Get.find();

  RxList<String> stringList = List.generate(108, (index) => '').obs;
  // Color properties
  Rx<Color> selectedTextColor = Colors.black.obs;
  Rx<Color> selectedCellColor = Colors.grey[100]!.obs;

  int currentIndex = 0;

  // Progress variables
  RxDouble progressValue = 0.0.obs;
  RxInt writeCount = 0.obs;

  // Session tracking
  DateTime? sessionStartTime;

  @override
  void onInit() {
    super.onInit();
    sessionStartTime = DateTime.now();
  }

  void addToStringList() {
    if (currentIndex < stringList.length) {
      stringList[currentIndex] = 'RAM';
      currentIndex++;
      writeCount.value++;
      progressValue.value = currentIndex / stringList.length;

      // Log Ram Naam Jaap action to Firebase Analytics
      if (Get.isRegistered<FirebaseService>()) {
        FirebaseService.instance.logEvent(
          name: 'ram_naam_written',
          parameters: {
            'count': writeCount.value,
            'progress_percentage': (progressValue.value * 100).round(),
            'session_duration_seconds': sessionStartTime != null
                ? DateTime.now().difference(sessionStartTime!).inSeconds
                : 0,
          },
        );

        // Log milestone events
        if (writeCount.value == 27) {
          // 25% complete
          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '25_percent',
              'count': writeCount.value,
            },
          );
        } else if (writeCount.value == 54) {
          // 50% complete
          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '50_percent',
              'count': writeCount.value,
            },
          );
        } else if (writeCount.value == 81) {
          // 75% complete
          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '75_percent',
              'count': writeCount.value,
            },
          );
        } else if (writeCount.value == 108) {
          // 100% complete
          final sessionDuration = sessionStartTime != null
              ? DateTime.now().difference(sessionStartTime!).inSeconds
              : 0;

          FirebaseService.instance.logRamNaamJaapSession(
            boxesFilled: writeCount.value,
            totalBoxes: 108,
            sessionDuration: sessionDuration,
            completionStatus: 'completed',
          );

          FirebaseService.instance.logEvent(
            name: 'ram_naam_milestone',
            parameters: {
              'milestone': '100_percent_completed',
              'count': writeCount.value,
              'session_duration_seconds': sessionDuration,
            },
          );
        }
      }
    }
  }

  // Function to change text color
  void changeTextColor(Color color) {
    selectedTextColor.value = color;
    update(); // Notify UI
  }

  // Function to change cell color
  void changeCellColor(Color color) {
    selectedCellColor.value = color;
    update(); // Notify UI
  }
}
