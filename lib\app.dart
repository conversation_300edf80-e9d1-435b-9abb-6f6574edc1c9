import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:login_flutter_app/src/utils/app_bindings.dart';
import 'package:login_flutter_app/src/utils/theme/theme.dart';
import 'package:login_flutter_app/src/features/core/controllers/theme_controller.dart';

class App extends StatelessWidget {
  const App({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Initialize theme controller
    final themeController = Get.put(ThemeController());

    return Obx(() => GetMaterialApp(
          /// -- README(Docs[3]) -- Bindings
          initialBinding: InitialBinding(),
          themeMode: themeController.themeMode,
          theme: TAppTheme.lightTheme,
          darkTheme: TAppTheme.darkTheme,
          debugShowCheckedModeBanner: false,

          /// -- README(Docs[4]) -- To use Screen Transitions here
          /// -- README(Docs[5]) -- Home Screen or Progress Indicator
          home:
              const Scaffold(body: Center(child: CircularProgressIndicator())),
        ));
  }
}
