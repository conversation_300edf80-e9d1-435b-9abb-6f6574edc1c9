import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:login_flutter_app/src/utils/app_bindings.dart';
import 'package:login_flutter_app/src/utils/theme/theme.dart';
import 'package:login_flutter_app/src/features/core/controllers/theme_controller.dart';
import 'package:login_flutter_app/src/features/core/controllers/language_controller.dart';
import 'package:login_flutter_app/src/localization/app_localizations.dart';

class App extends StatelessWidget {
  const App({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Initialize controllers
    final themeController = Get.put(ThemeController());
    final languageController = Get.put(LanguageController());

    return Obx(() => GetMaterialApp(
          /// -- README(Docs[3]) -- Bindings
          initialBinding: InitialBinding(),
          themeMode: themeController.themeMode,
          theme: TAppTheme.lightTheme,
          darkTheme: TAppTheme.darkTheme,
          debugShowCheckedModeBanner: false,

          /// -- Localization Support
          localizationsDelegates: AppLocalizations.localizationsDelegates,
          supportedLocales: AppLocalizations.supportedLocales,
          locale: languageController.currentLocale.value,

          /// -- README(Docs[4]) -- To use Screen Transitions here
          /// -- README(Docs[5]) -- Home Screen or Progress Indicator
          home:
              const Scaffold(body: Center(child: CircularProgressIndicator())),
        ));
  }
}
