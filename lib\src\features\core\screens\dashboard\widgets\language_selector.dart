import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../constants/colors.dart';
import '../../../controllers/language_controller.dart';
import '../../../../../localization/app_localizations.dart';

class LanguageSelector extends StatelessWidget {
  const LanguageSelector({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final languageController = Get.find<LanguageController>();
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final localizations = AppLocalizations.of(context)!;

    return Container(
      margin: const EdgeInsets.only(right: 10, top: 7, bottom: 7),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        color: tWhiteColor.withOpacity(0.2),
        border: Border.all(color: tWhiteColor.withOpacity(0.3), width: 1),
      ),
      child: Obx(() => PopupMenuButton<String>(
        icon: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              languageController.currentLanguage.flag,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(width: 4),
            Icon(
              Icons.arrow_drop_down,
              color: tWhiteColor,
              size: 16,
            ),
          ],
        ),
        onSelected: (String languageCode) {
          languageController.changeLanguage(languageCode);
        },
        itemBuilder: (BuildContext context) {
          return languageController.languages.map((language) {
            return PopupMenuItem<String>(
              value: language.code,
              child: Row(
                children: [
                  Text(
                    language.flag,
                    style: const TextStyle(fontSize: 18),
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        language.name,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                      Text(
                        language.nativeName,
                        style: TextStyle(
                          fontSize: 12,
                          color: isDark ? Colors.white70 : Colors.black54,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  if (languageController.currentLanguage.code == language.code)
                    Icon(
                      Icons.check,
                      color: tSpiritualSaffron,
                      size: 20,
                    ),
                ],
              ),
            );
          }).toList();
        },
        color: isDark ? Colors.grey[800] : Colors.white,
        elevation: 8,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      )),
    );
  }
}

class LanguageBottomSheet extends StatelessWidget {
  const LanguageBottomSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final languageController = Get.find<LanguageController>();
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final localizations = AppLocalizations.of(context)!;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[900] : Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[400],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 20),
          
          // Title
          Text(
            localizations.language,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: isDark ? Colors.white : Colors.black87,
            ),
          ),
          const SizedBox(height: 20),
          
          // Language options
          ...languageController.languages.map((language) {
            return Obx(() => ListTile(
              leading: Text(
                language.flag,
                style: const TextStyle(fontSize: 24),
              ),
              title: Text(
                language.name,
                style: TextStyle(
                  fontWeight: FontWeight.w500,
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              subtitle: Text(
                language.nativeName,
                style: TextStyle(
                  color: isDark ? Colors.white70 : Colors.black54,
                ),
              ),
              trailing: languageController.currentLanguage.code == language.code
                  ? Icon(
                      Icons.check_circle,
                      color: tSpiritualSaffron,
                    )
                  : null,
              onTap: () {
                languageController.changeLanguage(language.code);
                Get.back();
              },
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              tileColor: languageController.currentLanguage.code == language.code
                  ? tSpiritualSaffron.withOpacity(0.1)
                  : null,
            ));
          }).toList(),
          
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
