import 'package:flutter/material.dart';
import '../../../../../constants/colors.dart';
import '../../../../../constants/image_strings.dart';
import '../../../../../constants/text_strings.dart';

class SpiritualDeitiesSection extends StatelessWidget {
  const SpiritualDeitiesSection({
    Key? key,
    required this.txtTheme,
    required this.isDark,
  }) : super(key: key);

  final TextTheme txtTheme;
  final bool isDark;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 120,
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildDeityCard(
            name: tG<PERSON><PERSON>,
            icon: tGaneshIcon,
            fallbackIcon: Icons.star,
            colors: isDark
                ? [tSpiritualMaroon, tSpiritualDeepOrange]
                : [tSpiritualSaffron, tSpiritualOrange],
          ),
          const SizedBox(width: 15),
          _buildDeityCard(
            name: t<PERSON>hiva,
            icon: tShivaSymbol,
            fallbackIcon: Icons.self_improvement,
            colors: isDark
                ? [tSpiritualDeepOrange, tSpiritualOrange]
                : [tSpiritualLotus, tSpiritualSaffron],
          ),
          const SizedBox(width: 15),
          _buildDeityCard(
            name: tKrishna,
            icon: tKrishnaIcon,
            fallbackIcon: Icons.music_note,
            colors: isDark
                ? [tSpiritualOrange, tSpiritualGold]
                : [tSpiritualGold, tSpiritualYellow],
          ),
          const SizedBox(width: 15),
          _buildDeityCard(
            name: tHanuman,
            icon: tHanumanIcon,
            fallbackIcon: Icons.fitness_center,
            colors: isDark
                ? [tSpiritualGold, tSpiritualYellow]
                : [tSpiritualBrown, tSpiritualMaroon],
          ),
          const SizedBox(width: 15),
          _buildDeityCard(
            name: tDurga,
            icon: tDurgaIcon,
            fallbackIcon: Icons.shield,
            colors: isDark
                ? [tSpiritualMaroon, tSpiritualGold]
                : [tSpiritualDeepOrange, tSpiritualSaffron],
          ),
        ],
      ),
    );
  }

  Widget _buildDeityCard({
    required String name,
    required String icon,
    required IconData fallbackIcon,
    required List<Color> colors,
  }) {
    return GestureDetector(
      onTap: () {
        // Navigate to deity-specific content
      },
      child: Container(
        width: 100,
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          gradient: LinearGradient(
            colors: colors.map((color) => color.withOpacity(0.8)).toList(),
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          boxShadow: [
            BoxShadow(
              color: colors.first.withOpacity(0.3),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: tWhiteColor.withOpacity(0.2),
              ),
              child: Image.asset(
                icon,
                width: 30,
                height: 30,
                color: tWhiteColor,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    fallbackIcon,
                    size: 30,
                    color: tWhiteColor,
                  );
                },
              ),
            ),
            const SizedBox(height: 10),
            Text(
              name,
              style: TextStyle(
                color: tWhiteColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
