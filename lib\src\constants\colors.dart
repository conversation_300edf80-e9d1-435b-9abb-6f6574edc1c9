import 'package:flutter/material.dart';

/* -- LIST OF ALL COLORS -- */

const tPrimaryColor = Color(0xFFFFE400);
const tSecondaryColor = Color(0xFF272727);
const tAccentColor = Color(0xFF001BFF);

const tWhiteColor = Colors.white;
const tDarkColor = Color(0xff272727);
const tCardBgColor = Color(0xFFF7F5F1);

// -- SOCIAL Button COLORS
const tGoogleBgColor = Color(0xFFDFEFFF);
const tGoogleForegroundColor = Color(0xFF167EE6);
const tFacebookBgColor = Color(0xFF1877F2);

// -- ON-BOARDING COLORS
const tOnBoardingPage1Color = Colors.white;
const tOnBoardingPage2Color = Color(0xfffddcdf);
const tOnBoardingPage3Color = Color(0xffffdcbd);

// -- SPIRITUAL COLORS
const tSpiritualOrange = Color(0xFFFF6B35); // Saffron/Orange
const tSpiritualGold = Color(0xFFFFD700); // Gold
const tSpiritualMaroon = Color(0xFF8B0000); // Deep Red/Maroon
const tSpiritualLotus = Color(0xFFFFB6C1); // Light Pink (Lotus)
const tSpiritualSaffron = Color(0xFFFF9933); // Saffron
const tSpiritualDeepOrange = Color(0xFFFF4500); // Deep Orange
const tSpiritualCream = Color(0xFFFFFDD0); // Cream
const tSpiritualLightOrange = Color(0xFFFFE4B5); // Light Orange
const tSpiritualBrown = Color(0xFF8B4513); // Spiritual Brown
const tSpiritualYellow = Color(0xFFFFFF00); // Bright Yellow


