import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../constants/colors.dart';
import '../../../../../constants/text_strings.dart';
import '../../../../writer/screens/writer_108_box/writer_108_box_screen.dart';

class SpiritualQuickActions extends StatelessWidget {
  const SpiritualQuickActions({
    Key? key,
    required this.txtTheme,
    required this.isDark,
  }) : super(key: key);

  final TextTheme txtTheme;
  final bool isDark;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildQuickActionButton(
                title: tStartJaap,
                icon: Icons.edit,
                colors: isDark
                    ? [tSpiritualMaroon, tSpiritualDeepOrange]
                    : [tSpiritualSaffron, tSpiritualOrange],
                onTap: () => Get.to(() => const Writer108BoxScreen()),
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: _buildQuickActionButton(
                title: tPlayM<PERSON>ras,
                icon: Icons.music_note,
                colors: isDark
                    ? [tSpiritualDeepOrange, tSpiritualOrange]
                    : [tSpiritualLotus, tSpiritualSaffron],
                onTap: () {
                  // Navigate to mantras screen
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: 15),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionButton(
                title: tDailyPrayer,
                icon: Icons.self_improvement,
                colors: isDark
                    ? [tSpiritualOrange, tSpiritualGold]
                    : [tSpiritualGold, tSpiritualYellow],
                onTap: () {
                  // Navigate to daily prayer screen
                },
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: _buildQuickActionButton(
                title: tSpiritualCalendar,
                icon: Icons.calendar_today,
                colors: isDark
                    ? [tSpiritualGold, tSpiritualYellow]
                    : [tSpiritualBrown, tSpiritualMaroon],
                onTap: () {
                  // Navigate to spiritual calendar screen
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickActionButton({
    required String title,
    required IconData icon,
    required List<Color> colors,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 80,
        padding: const EdgeInsets.all(15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          gradient: LinearGradient(
            colors: colors.map((color) => color.withOpacity(0.8)).toList(),
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: colors.first.withOpacity(0.3),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 45,
              height: 45,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: tWhiteColor.withOpacity(0.2),
              ),
              child: Icon(
                icon,
                color: tWhiteColor,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: tWhiteColor,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: tWhiteColor.withOpacity(0.7),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
