import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart';

import 'app_localizations_en.dart';
import 'app_localizations_hi.dart';

abstract class AppLocalizations {
  AppLocalizations(String locale)
      : localeName = Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  static List<LocalizationsDelegate<dynamic>> get localizationsDelegates => [
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  static const List<Locale> supportedLocales = <Locale>[
    Locale('en', 'US'),
    Locale('hi', 'IN'),
  ];

  // App Name
  String get appName;

  // Welcome & Authentication
  String get welcome;
  String get getStarted;
  String get login;
  String get signup;
  String get email;
  String get password;
  String get confirmPassword;
  String get forgotPassword;
  String get rememberMe;
  String get dontHaveAccount;
  String get alreadyHaveAccount;
  String get createAccount;
  String get or;
  String get signInWithGoogle;
  String get signInWithFacebook;

  // Spiritual Dashboard
  String get spiritualGreeting;
  String get spiritualSubGreeting;
  String get todaysMantra;
  String get spiritualPractices;
  String get ramNaamJaap;
  String get meditation;
  String get prayers;
  String get temple;
  String get sacredTexts;
  String get spiritualCalendar;
  String get quickActions;
  String get startJaap;
  String get playMantras;
  String get dailyPrayer;
  String get spiritualQuote;
  String get blessings;
  String get auspiciousTime;
  String get festivals;
  String get deities;
  String get ganesh;
  String get shiva;
  String get krishna;
  String get hanuman;
  String get durga;
  String get ram;

  // Ram Naam Jaap
  String get ramNaamJaapTitle;
  String get ramNaamJaapSubtitle;
  String get writeRamName;
  String get startWritingRam;
  String get jaapCount;
  String get ramMantra;
  String get ramChalisa;
  String get ramTeaching;
  String get ramDevotion;
  String get todaysRamGuidance;

  // Writer Screen
  String get jaap;
  String get writeRam;
  String get ramInHindi;
  String get ramInEnglish;
  String get textColor;
  String get backgroundColor;
  String get customizeColors;
  String get progress;
  String get completed;

  // Common
  String get profile;
  String get settings;
  String get language;
  String get theme;
  String get darkMode;
  String get lightMode;
  String get save;
  String get cancel;
  String get ok;
  String get yes;
  String get no;
  String get back;
  String get next;
  String get done;
  String get loading;
  String get error;
  String get success;

  // Mantras and Spiritual Content
  String get omNamahShivaya;
  String get shrirRamJayRam;
  String get raghukulRiti;
  String get raghukulRitiTranslation;
  String get brahmuMuhurta;
  String get auspiciousTimeDesc;
  String get buddhaQuote;
  String get todayAuspicious;

  // Other Practices
  String get otherSpiritualPractices;
  String get innerPeace;
  String get dailyPrayers;
  String get sacredPlaces;
  String get fortyVerses;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'hi'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  switch (locale.languageCode) {
    case 'hi':
      return AppLocalizationsHi();
    case 'en':
    default:
      return AppLocalizationsEn();
  }
}
