// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      throw UnsupportedError(
        'DefaultFirebaseOptions have not been configured for web - '
        'you can reconfigure this by running the FlutterFire CLI again.',
      );
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyB5m-lFIRj5fqZNIckwEbJBXFeppv3PQDg',
    appId: '1:651191479173:android:1b31f5b4c571ce069172ec',
    messagingSenderId: '651191479173',
    projectId: 'flutter-login-app-72e15',
    storageBucket: 'flutter-login-app-72e15.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCwqydnSdwNjyD2o8Grj8CwSE2TIx9YDDc',
    appId: '1:651191479173:ios:26b42a917b56c96c9172ec',
    messagingSenderId: '651191479173',
    projectId: 'flutter-login-app-72e15',
    storageBucket: 'flutter-login-app-72e15.appspot.com',
    androidClientId: '651191479173-qq9cf2dk9cc3d5dp3bs9bcu445o5985n.apps.googleusercontent.com',
    iosClientId: '651191479173-ku2agvtjqs7d9e3sc1qoucieijts4tki.apps.googleusercontent.com',
    iosBundleId: 'com.codingwitht.loginFlutterApp',
  );
}
