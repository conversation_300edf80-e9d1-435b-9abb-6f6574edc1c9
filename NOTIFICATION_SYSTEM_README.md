# Firebase Cloud Messaging (FCM) Notification System

## Overview

This document provides a comprehensive guide to the Firebase Cloud Messaging implementation in the Ram Naam Jaap app. The system provides granular notification control with 8 different categories, allowing users to customize their spiritual journey experience.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Dependencies](#dependencies)
3. [File Structure](#file-structure)
4. [Implementation Details](#implementation-details)
5. [Notification Categories](#notification-categories)
6. [User Interface Components](#user-interface-components)
7. [Configuration](#configuration)
8. [Testing](#testing)
9. [Customization Guide](#customization-guide)
10. [Troubleshooting](#troubleshooting)

## Architecture Overview

The notification system follows a modular architecture with the following components:

```
┌─────────────────────────────────────────────────────────────┐
│                    Firebase Console                         │
│                  (Send Notifications)                      │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Firebase Cloud Messaging                    │
│                  (Topic-based Delivery)                    │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 Mobile Application                         │
│  ┌─────────────────┬─────────────────┬─────────────────┐   │
│  │ NotificationService │ UI Components │ User Preferences │   │
│  │  - FCM Setup       │ - Settings UI │ - Storage       │   │
│  │  - Topic Mgmt      │ - Toggles     │ - Persistence   │   │
│  │  - Message Handle  │ - Navigation  │ - Validation    │   │
│  └─────────────────┴─────────────────┴─────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## Dependencies

### Required Dependencies (pubspec.yaml)

```yaml
dependencies:
  # Firebase Core
  firebase_core: ^2.24.0
  firebase_messaging: ^14.7.0
  firebase_analytics: ^10.7.0
  firebase_crashlytics: ^3.4.0

  # State Management & Storage
  get: ^4.6.6
  get_storage: ^2.1.1

  # UI Components
  flutter:
    sdk: flutter
```

### Installation

```bash
# Install dependencies
flutter pub get

# Configure Firebase (if not already done)
flutterfire configure
```

## File Structure

```
lib/src/
├── services/
│   └── notification_service.dart          # Core FCM service
├── features/core/screens/
│   └── notifications/
│       └── notification_preferences_screen.dart  # Settings UI
├── features/core/controllers/
│   └── language_controller.dart           # Extended for preferences
└── main.dart                             # FCM initialization
```

## Implementation Details

### 1. Core Service (notification_service.dart)

The `NotificationService` class handles all FCM operations:

#### Key Features:

- **FCM Token Management**: Automatic token generation and refresh
- **Topic Subscriptions**: Dynamic subscription/unsubscription
- **Permission Handling**: Request and manage notification permissions
- **Message Processing**: Handle foreground, background, and terminated states
- **Preference Storage**: Persistent user preferences with GetStorage

#### Core Methods:

```dart
class NotificationService extends GetxService {
  // Initialize FCM
  Future<void> _initializeFirebaseMessaging()

  // Permission management
  Future<void> _requestNotificationPermissions()

  // Token management
  Future<void> _getFCMToken()

  // Topic management
  Future<void> _subscribeToTopics()
  Future<void> setCategoryEnabled(NotificationCategory category, bool enabled)

  // Master control
  Future<void> setMasterNotificationEnabled(bool enabled)

  // Message handling
  void _handleForegroundMessage(RemoteMessage message)
  void _handleMessageOpenedApp(RemoteMessage message)
}
```

### 2. Notification Categories

The system supports 8 predefined categories:

```dart
enum NotificationCategory {
  dailyReminders('daily_reminders', 'Daily Reminders', 'Get reminded to practice Ram Naam Jaap daily'),
  spiritualQuotes('spiritual_quotes', 'Spiritual Quotes', 'Receive inspiring spiritual quotes and teachings'),
  poojaTimings('pooja_timings', 'Pooja Timings', 'Get notified about auspicious pooja times'),
  spiritualNews('spiritual_news', 'Spiritual News', 'Stay updated with spiritual events and news'),
  festivalAlerts('festival_alerts', 'Festival Alerts', 'Get notified about upcoming spiritual festivals'),
  ramNavami('ram_navami', 'Ram Navami Special', 'Special notifications for Ram Navami celebrations'),
  weeklyProgress('weekly_progress', 'Weekly Progress', 'Your weekly spiritual practice summary'),
  motivationalMessages('motivational_messages', 'Motivational Messages', 'Encouraging messages for your spiritual journey');
}
```

### 3. User Interface Components

#### Navigation Drawer Integration

```dart
// In spiritual_dashboard.dart
_buildDrawerItem(Icons.notifications, 'Notifications', isDark, () {
  Get.back();
  Get.to(() => const NotificationPreferencesScreen());
}),
```

#### Preferences Setup Screen

```dart
// In preferences_setup_screen.dart
_buildNotificationPreferencesCard(
  context,
  localizations,
  isDark,
),
```

#### Dedicated Notification Settings Screen

The `NotificationPreferencesScreen` provides:

- Master notification toggle
- Individual category toggles
- Visual feedback with snackbars
- Consistent spiritual theming

## Configuration

### 1. Firebase Console Setup

1. **Create Firebase Project**
2. **Enable Cloud Messaging**
3. **Configure Topics** (optional - topics are created automatically)
4. **Set up Server Key** for backend integration

### 2. Android Configuration

Ensure `android/app/google-services.json` is properly configured.

### 3. iOS Configuration

Ensure `ios/Runner/GoogleService-Info.plist` is properly configured.

### 4. Background Message Handler

```dart
// In main.dart
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('Handling background message: ${message.messageId}');
  // Handle background message here
}

// Initialize in main()
FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
```

## Testing

### 1. Local Testing

```dart
// Test notification permissions
final notificationService = Get.find<NotificationService>();
print('Permissions granted: ${notificationService.isAvailable}');

// Test topic subscription
await notificationService.setCategoryEnabled(
  NotificationCategory.dailyReminders,
  true
);
```

### 2. Firebase Console Testing

1. Go to Firebase Console → Cloud Messaging
2. Create new campaign
3. Select target: Topic
4. Choose topic (e.g., `daily_reminders`)
5. Send test message

### 3. Manual Testing Checklist

- [ ] App requests notification permissions on first launch
- [ ] FCM token is generated successfully
- [ ] All 8 topics are subscribed by default
- [ ] Master toggle enables/disables all notifications
- [ ] Individual category toggles work correctly
- [ ] Settings persist across app restarts
- [ ] Foreground notifications show as snackbars
- [ ] Background notifications appear in system tray
- [ ] Tapping notifications navigates correctly

## Customization Guide

### Adding New Notification Categories

1. **Update Enum**:

```dart
enum NotificationCategory {
  // Existing categories...
  newCategory('new_category', 'New Category', 'Description of new category');
}
```

2. **Update UI Icons and Colors**:

```dart
IconData _getCategoryIcon(NotificationCategory category) {
  switch (category) {
    // Existing cases...
    case NotificationCategory.newCategory:
      return Icons.new_icon;
  }
}

Color _getCategoryColor(NotificationCategory category) {
  switch (category) {
    // Existing cases...
    case NotificationCategory.newCategory:
      return Colors.newColor;
  }
}
```

3. **Update Default Preferences**:
   The new category will automatically be included in default preferences.

### Modifying UI Themes

Update colors in the notification preferences screen:

```dart
// Change primary color
color: tSpiritualSaffron.withOpacity(0.2),

// Change accent color
activeColor: tSpiritualSaffron,

// Change background gradient
gradient: LinearGradient(
  colors: [
    tSpiritualSaffron.withOpacity(0.1),
    tSpiritualOrange.withOpacity(0.1),
  ],
),
```

### Backend Integration

To send notifications from your backend:

```javascript
// Node.js example using Firebase Admin SDK
const admin = require("firebase-admin");

// Send to specific topic
const message = {
  notification: {
    title: "Daily Reminder",
    body: "Time for your Ram Naam Jaap practice!",
  },
  data: {
    screen: "ram_naam_jaap",
    action: "open_writer",
  },
  topic: "daily_reminders",
};

admin.messaging().send(message);
```

### Custom Message Handling

Modify navigation logic in `_handleNotificationNavigation`:

```dart
void _handleNotificationNavigation(RemoteMessage message) {
  String? screen = message.data['screen'];
  String? action = message.data['action'];

  switch (screen) {
    case 'ram_naam_jaap':
      Get.to(() => const Writer108BoxScreen());
      break;
    case 'custom_screen':
      Get.to(() => const CustomScreen());
      break;
    // Add more cases as needed
  }
}
```

## Troubleshooting

### Common Issues

1. **Notifications not received**

   - Check if permissions are granted
   - Verify FCM token generation
   - Ensure topic subscription is successful
   - Check Firebase Console message status

2. **App crashes on notification**

   - Verify background message handler is properly set up
   - Check for null safety in message handling
   - Ensure all required dependencies are installed

3. **Preferences not persisting**

   - Verify GetStorage initialization in main.dart
   - Check storage permissions
   - Ensure proper error handling in save/load methods

4. **Topics not subscribing**
   - Check internet connectivity
   - Verify Firebase configuration
   - Ensure proper error handling in subscription methods

### Debug Commands

```dart
// Enable debug logging
print('FCM Token: ${notificationService.fcmToken.value}');
print('Master enabled: ${notificationService.isMasterNotificationEnabled.value}');
print('Category preferences: ${notificationService.notificationPreferences.value}');
```

### Performance Optimization

1. **Lazy Loading**: Services are initialized only when needed
2. **Efficient Storage**: Only changed preferences are saved
3. **Background Processing**: Heavy operations run in background
4. **Memory Management**: Proper disposal of listeners and controllers

## Security Considerations

1. **Token Security**: FCM tokens should be treated as sensitive data
2. **Permission Validation**: Always check permissions before operations
3. **Input Validation**: Validate all notification data before processing
4. **Error Handling**: Implement comprehensive error handling

## Future Enhancements

1. **Scheduled Notifications**: Local notifications for offline reminders
2. **Rich Media**: Support for images and videos in notifications
3. **Interactive Notifications**: Action buttons in notifications
4. **Analytics Integration**: Track notification engagement metrics
5. **A/B Testing**: Test different notification strategies

## Advanced Configuration

### Environment-Specific Setup

#### Development Environment

```dart
// In notification_service.dart
static const bool _isDevelopment = kDebugMode;

Future<void> _subscribeToTopics() async {
  String topicPrefix = _isDevelopment ? 'dev_' : '';

  for (NotificationCategory category in NotificationCategory.values) {
    String topicName = '$topicPrefix${category.key}';
    await _messaging.subscribeToTopic(topicName);
  }
}
```

#### Production Environment

```dart
// Use production topics without prefix
String topicName = category.key;
```

### Notification Scheduling

#### Server-Side Scheduling (Recommended)

```javascript
// Using Firebase Functions
const functions = require("firebase-functions");
const admin = require("firebase-admin");

exports.sendDailyReminder = functions.pubsub
  .schedule("0 7 * * *") // Daily at 7 AM
  .timeZone("Asia/Kolkata")
  .onRun(async (context) => {
    const message = {
      notification: {
        title: "Good Morning! 🌅",
        body: "Start your day with Ram Naam Jaap",
      },
      topic: "daily_reminders",
    };

    return admin.messaging().send(message);
  });
```

#### Client-Side Local Notifications

```dart
// For offline reminders
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class LocalNotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  static Future<void> scheduleDailyReminder() async {
    await _notifications.zonedSchedule(
      0,
      'Daily Practice Reminder',
      'Time for your spiritual practice!',
      _nextInstanceOfTime(7, 0), // 7:00 AM
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'daily_reminders',
          'Daily Reminders',
          channelDescription: 'Daily spiritual practice reminders',
        ),
      ),
      androidAllowWhileIdle: true,
      uiLocalNotificationDateInterpretation:
          UILocalNotificationDateInterpretation.absoluteTime,
      matchDateTimeComponents: DateTimeComponents.time,
    );
  }
}
```

### Analytics Integration

#### Track Notification Engagement

```dart
// In notification_service.dart
void _handleMessageOpenedApp(RemoteMessage message) {
  // Log notification opened event
  if (Get.isRegistered<FirebaseService>()) {
    FirebaseService.instance.logEvent(
      name: 'notification_opened',
      parameters: {
        'notification_id': message.messageId ?? 'unknown',
        'category': message.data['category'] ?? 'unknown',
        'source': 'fcm',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  _handleNotificationNavigation(message);
}
```

#### Track Preference Changes

```dart
Future<void> setCategoryEnabled(NotificationCategory category, bool enabled) async {
  // Log preference change
  if (Get.isRegistered<FirebaseService>()) {
    FirebaseService.instance.logEvent(
      name: 'notification_preference_changed',
      parameters: {
        'category': category.key,
        'enabled': enabled,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  // Existing implementation...
}
```

### Multi-Language Support

#### Localized Notification Categories

```dart
// In app_localizations.dart
class AppLocalizations {
  // Notification category titles
  String get dailyRemindersTitle => Intl.message(
    'Daily Reminders',
    name: 'dailyRemindersTitle',
  );

  String get spiritualQuotesTitle => Intl.message(
    'Spiritual Quotes',
    name: 'spiritualQuotesTitle',
  );

  // Notification category descriptions
  String get dailyRemindersDesc => Intl.message(
    'Get reminded to practice Ram Naam Jaap daily',
    name: 'dailyRemindersDesc',
  );
}
```

#### Server-Side Localization

```javascript
// Firebase Functions with localization
const getLocalizedMessage = (category, language) => {
  const messages = {
    en: {
      daily_reminders: {
        title: "Daily Reminder",
        body: "Time for your Ram Naam Jaap practice!",
      },
    },
    hi: {
      daily_reminders: {
        title: "दैनिक अनुस्मारक",
        body: "राम नाम जाप का समय!",
      },
    },
  };

  return messages[language]?.[category] || messages["en"][category];
};
```

### Performance Monitoring

#### FCM Performance Metrics

```dart
class NotificationMetrics {
  static final Map<String, int> _deliveryCount = {};
  static final Map<String, int> _openCount = {};

  static void trackDelivery(String category) {
    _deliveryCount[category] = (_deliveryCount[category] ?? 0) + 1;
  }

  static void trackOpen(String category) {
    _openCount[category] = (_openCount[category] ?? 0) + 1;
  }

  static double getOpenRate(String category) {
    final delivered = _deliveryCount[category] ?? 0;
    final opened = _openCount[category] ?? 0;
    return delivered > 0 ? opened / delivered : 0.0;
  }
}
```

### Error Handling & Logging

#### Comprehensive Error Handling

```dart
class NotificationErrorHandler {
  static void handleError(String operation, dynamic error, StackTrace? stackTrace) {
    // Log to Crashlytics
    if (Get.isRegistered<FirebaseService>()) {
      FirebaseService.instance.logError(
        error,
        stackTrace,
        fatal: false,
        context: 'NotificationService.$operation',
      );
    }

    // Log locally for debugging
    print('Notification Error [$operation]: $error');

    // Show user-friendly message if needed
    if (operation.contains('permission')) {
      Get.snackbar(
        'Notification Permission',
        'Please enable notifications in device settings for the best experience',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.orange.withOpacity(0.8),
        colorText: Colors.white,
      );
    }
  }
}
```

### Testing Strategies

#### Unit Tests

```dart
// test/notification_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';

class MockFirebaseMessaging extends Mock implements FirebaseMessaging {}

void main() {
  group('NotificationService Tests', () {
    late NotificationService service;
    late MockFirebaseMessaging mockMessaging;

    setUp(() {
      mockMessaging = MockFirebaseMessaging();
      service = NotificationService();
    });

    test('should subscribe to topics when enabled', () async {
      // Test topic subscription logic
      await service.setCategoryEnabled(NotificationCategory.dailyReminders, true);
      verify(mockMessaging.subscribeToTopic('daily_reminders')).called(1);
    });

    test('should unsubscribe from topics when disabled', () async {
      // Test topic unsubscription logic
      await service.setCategoryEnabled(NotificationCategory.dailyReminders, false);
      verify(mockMessaging.unsubscribeFromTopic('daily_reminders')).called(1);
    });
  });
}
```

#### Integration Tests

```dart
// integration_test/notification_flow_test.dart
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Notification Flow Tests', () {
    testWidgets('should navigate to notification settings', (tester) async {
      // Test navigation to notification settings
      await tester.pumpWidget(MyApp());
      await tester.pumpAndSettle();

      // Open navigation drawer
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // Tap notifications option
      await tester.tap(find.text('Notifications'));
      await tester.pumpAndSettle();

      // Verify notification settings screen is displayed
      expect(find.text('Notification Settings'), findsOneWidget);
    });
  });
}
```

### Deployment Checklist

#### Pre-Production Checklist

- [ ] Firebase project configured for production
- [ ] FCM server key secured
- [ ] All notification categories tested
- [ ] Permission flows tested on different devices
- [ ] Background message handling verified
- [ ] Analytics events implemented
- [ ] Error handling comprehensive
- [ ] Performance metrics in place
- [ ] Multi-language support (if applicable)
- [ ] Local notification fallbacks (if applicable)

#### Production Monitoring

```dart
// Monitor key metrics
class NotificationMonitoring {
  static void logSystemHealth() {
    final service = Get.find<NotificationService>();

    FirebaseService.instance.logEvent(
      name: 'notification_system_health',
      parameters: {
        'fcm_available': service.isAvailable,
        'master_enabled': service.isMasterNotificationEnabled.value,
        'active_categories': service.notificationPreferences.values
            .where((enabled) => enabled).length,
        'total_categories': NotificationCategory.values.length,
      },
    );
  }
}
```

## Support

For issues or questions:

1. Check this documentation first
2. Review Firebase Console logs
3. Check device logs for error messages
4. Test with different devices and OS versions
5. Consult Firebase documentation for FCM-specific issues
6. Check GitHub issues for known problems

### Common Support Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Flutter Firebase Messaging Plugin](https://pub.dev/packages/firebase_messaging)
- [GetX State Management](https://pub.dev/packages/get)
- [GetStorage Documentation](https://pub.dev/packages/get_storage)

---

**Last Updated**: January 2024
**Version**: 1.0.0
**Compatibility**: Flutter 3.13.9+, Firebase SDK 2.24.0+
**Maintainer**: Development Team
**License**: MIT
