import 'package:get/get.dart';
import '../../../repository/authentication_repository/authentication_repository.dart';
import '../../core/screens/dashboard/spiritual_dashboard.dart';
import '../../core/screens/preferences/preferences_setup_screen.dart';
import '../../core/controllers/user_preferences_controller.dart';

class OTPController extends GetxController {
  static OTPController get instance => Get.find();

  void verifyOTP(String otp) async {
    var isVerified = await AuthenticationRepository.instance.verifyOTP(otp);
    if (isVerified) {
      // Check preferences after OTP verification
      final preferencesController = Get.put(UserPreferencesController());
      preferencesController.initializeFromCurrentState();

      if (preferencesController.needsPreferencesSetup) {
        Get.offAll(() => const PreferencesSetupScreen());
      } else {
        Get.offAll(() => const SpiritualDashboard());
      }
    } else {
      Get.back();
    }
  }
}
