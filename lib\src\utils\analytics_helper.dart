import 'package:get/get.dart';
import 'package:login_flutter_app/src/services/firebase_service.dart';

/// Helper class for Firebase Analytics tracking throughout the app
class AnalyticsHelper {
  static FirebaseService? get _firebaseService {
    if (Get.isRegistered<FirebaseService>()) {
      return FirebaseService.instance;
    }
    return null;
  }

  // AUTHENTICATION EVENTS

  /// Track user login events
  static Future<void> trackLogin({
    required String method,
    String? userId,
    bool success = true,
  }) async {
    if (_firebaseService == null) return;

    if (success) {
      await _firebaseService!.logLogin(
        loginMethod: method,
        userId: userId,
      );
    } else {
      await _firebaseService!.logEvent(
        name: 'login_failed',
        parameters: {
          'method': method,
          'error_type': 'authentication_failed',
        },
      );
    }
  }

  /// Track user signup events
  static Future<void> trackSignup({
    required String method,
    String? userId,
    bool success = true,
  }) async {
    if (_firebaseService == null) return;

    if (success) {
      await _firebaseService!.logSignUp(
        signUpMethod: method,
        userId: userId,
      );
    } else {
      await _firebaseService!.logEvent(
        name: 'signup_failed',
        parameters: {
          'method': method,
          'error_type': 'registration_failed',
        },
      );
    }
  }

  // SPIRITUAL PRACTICE EVENTS

  /// Track Ram Naam Jaap session start
  static Future<void> trackRamNaamJaapStart({
    String source = 'unknown',
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logEvent(
      name: 'ram_naam_jaap_started',
      parameters: {
        'source': source,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  /// Track individual Ram writing action
  static Future<void> trackRamWritten({
    required int count,
    required double progressPercentage,
    required int sessionDurationSeconds,
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logEvent(
      name: 'ram_naam_written',
      parameters: {
        'count': count,
        'progress_percentage': progressPercentage.round(),
        'session_duration_seconds': sessionDurationSeconds,
      },
    );
  }

  /// Track Ram Naam Jaap milestones
  static Future<void> trackRamNaamJaapMilestone({
    required String milestone,
    required int count,
    int? sessionDurationSeconds,
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logEvent(
      name: 'ram_naam_milestone',
      parameters: {
        'milestone': milestone,
        'count': count,
        if (sessionDurationSeconds != null) 'session_duration_seconds': sessionDurationSeconds,
      },
    );
  }

  /// Track completed Ram Naam Jaap session
  static Future<void> trackRamNaamJaapCompleted({
    required int totalCount,
    required int sessionDurationSeconds,
    String completionStatus = 'completed',
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logRamNaamJaapSession(
      boxesFilled: totalCount,
      totalBoxes: 108,
      sessionDuration: sessionDurationSeconds,
      completionStatus: completionStatus,
    );
  }

  // UI INTERACTION EVENTS

  /// Track screen views
  static Future<void> trackScreenView({
    required String screenName,
    String? screenClass,
    Map<String, Object>? parameters,
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logScreenView(
      screenName: screenName,
      screenClass: screenClass ?? screenName,
      parameters: parameters,
    );
  }

  /// Track button clicks
  static Future<void> trackButtonClick({
    required String buttonName,
    required String screenName,
    Map<String, Object>? additionalParams,
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logEvent(
      name: 'button_click',
      parameters: {
        'button_name': buttonName,
        'screen_name': screenName,
        ...?additionalParams,
      },
    );
  }

  /// Track theme changes
  static Future<void> trackThemeChange({
    required String newTheme,
    required String previousTheme,
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logEvent(
      name: 'theme_changed',
      parameters: {
        'new_theme': newTheme,
        'previous_theme': previousTheme,
      },
    );
  }

  /// Track language changes
  static Future<void> trackLanguageChange({
    required String newLanguage,
    required String previousLanguage,
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logEvent(
      name: 'language_changed',
      parameters: {
        'new_language': newLanguage,
        'previous_language': previousLanguage,
      },
    );
  }

  // CUSTOMIZATION EVENTS

  /// Track color customization in Ram Naam Jaap
  static Future<void> trackColorCustomization({
    required String colorType, // 'text' or 'background'
    required String colorValue,
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logEvent(
      name: 'ram_naam_color_changed',
      parameters: {
        'color_type': colorType,
        'color_value': colorValue,
      },
    );
  }

  // ERROR TRACKING

  /// Track errors with context
  static Future<void> trackError({
    required dynamic error,
    StackTrace? stackTrace,
    required String context,
    Map<String, String>? customKeys,
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logError(
      error: error,
      stackTrace: stackTrace,
      reason: 'Error in $context',
      customKeys: {
        'context': context,
        'timestamp': DateTime.now().toIso8601String(),
        ...?customKeys,
      },
    );
  }

  // USER PROPERTIES

  /// Set user properties for analytics
  static Future<void> setUserProperties({
    String? userId,
    String? userType,
    String? preferredLanguage,
    String? preferredTheme,
    Map<String, String>? customProperties,
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.setUserProperties(
      userId: userId,
      userType: userType,
      preferredLanguage: preferredLanguage,
      customProperties: {
        if (preferredTheme != null) 'preferred_theme': preferredTheme,
        ...?customProperties,
      },
    );
  }

  // ENGAGEMENT EVENTS

  /// Track app session start
  static Future<void> trackSessionStart() async {
    if (_firebaseService == null) return;

    await _firebaseService!.logEvent(
      name: 'session_start',
      parameters: {
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      },
    );
  }

  /// Track feature usage
  static Future<void> trackFeatureUsage({
    required String featureName,
    Map<String, Object>? parameters,
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logEvent(
      name: 'feature_used',
      parameters: {
        'feature_name': featureName,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        ...?parameters,
      },
    );
  }

  /// Track spiritual practice engagement
  static Future<void> trackSpiritualPractice({
    required String practiceType,
    int? durationSeconds,
    Map<String, Object>? additionalParams,
  }) async {
    if (_firebaseService == null) return;

    await _firebaseService!.logSpiritualPractice(
      practiceType: practiceType,
      duration: durationSeconds,
      additionalParams: additionalParams,
    );
  }
}
