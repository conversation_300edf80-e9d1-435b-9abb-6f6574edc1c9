import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../constants/colors.dart';
import '../../../../../constants/image_strings.dart';
import '../../../../../constants/text_strings.dart';
import '../../../controllers/theme_controller.dart';
import '../../../controllers/user_preferences_controller.dart';
import '../../preferences/preferences_setup_screen.dart';
import 'language_selector.dart';

class SpiritualAppBar extends StatelessWidget implements PreferredSizeWidget {
  const SpiritualAppBar({
    Key? key,
    required this.isDark,
  }) : super(key: key);

  final bool isDark;

  @override
  Widget build(BuildContext context) {
    return AppBar(
      elevation: 0,
      centerTitle: true,
      backgroundColor: Colors.transparent,
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: isDark
                ? [
                    tSpiritualMaroon.withOpacity(0.8),
                    tSpiritualDeepOrange.withOpacity(0.8)
                  ]
                : [
                    tSpiritualSaffron.withOpacity(0.9),
                    tSpiritualOrange.withOpacity(0.9)
                  ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      leading: Builder(
        builder: (context) => IconButton(
          icon: Icon(Icons.menu, color: tWhiteColor),
          onPressed: () => Scaffold.of(context).openDrawer(),
        ),
      ),
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 30,
            height: 30,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: tWhiteColor.withOpacity(0.2),
            ),
            child: _buildImageOrIcon(
              tOmSymbol,
              Icons.self_improvement,
              20,
              tWhiteColor,
            ),
          ),
          const SizedBox(width: 8),
          Text(
            'Ram Naam Jaap',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                  color: tWhiteColor,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ],
      ),
      actions: [
        // Language Selector
        const LanguageSelector(),

        // Theme Toggle Button
        Container(
          margin: const EdgeInsets.only(right: 10, top: 7, bottom: 7),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            color: tWhiteColor.withOpacity(0.2),
            border: Border.all(color: tWhiteColor.withOpacity(0.3), width: 1),
          ),
          child: Obx(() {
            final themeController = Get.find<ThemeController>();
            return IconButton(
              onPressed: () => themeController.toggleTheme(),
              icon: Icon(
                themeController.isDarkMode.value
                    ? Icons.light_mode
                    : Icons.dark_mode,
                color: tWhiteColor,
                size: 24,
              ),
            );
          }),
        ),

        // Profile Button
        Container(
          margin: const EdgeInsets.only(right: 15, top: 7, bottom: 7),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(25),
            color: tWhiteColor.withOpacity(0.2),
            border: Border.all(color: tWhiteColor.withOpacity(0.3), width: 1),
          ),
          child: PopupMenuButton<String>(
            icon: const Icon(
              Icons.account_circle,
              color: tWhiteColor,
              size: 28,
            ),
            onSelected: (String value) {
              if (value == 'preferences') {
                Get.to(() => const PreferencesSetupScreen());
              } else if (value == 'reset_preferences') {
                final preferencesController =
                    Get.find<UserPreferencesController>();
                preferencesController.resetPreferencesSetup();
                Get.snackbar(
                  'Preferences Reset',
                  'Your preferences have been reset. Please set them up again.',
                  snackPosition: SnackPosition.BOTTOM,
                );
                Get.to(() => const PreferencesSetupScreen());
              }
            },
            itemBuilder: (BuildContext context) {
              return [
                const PopupMenuItem<String>(
                  value: 'preferences',
                  child: Row(
                    children: [
                      Icon(Icons.settings),
                      SizedBox(width: 8),
                      Text('Preferences'),
                    ],
                  ),
                ),
                const PopupMenuItem<String>(
                  value: 'reset_preferences',
                  child: Row(
                    children: [
                      Icon(Icons.refresh),
                      SizedBox(width: 8),
                      Text('Reset Preferences'),
                    ],
                  ),
                ),
              ];
            },
            color: Colors.white,
            elevation: 8,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
      ],
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(60);

  Widget _buildImageOrIcon(
      String imagePath, IconData fallbackIcon, double size, Color color) {
    return Image.asset(
      imagePath,
      width: size,
      height: size,
      color: color,
      errorBuilder: (context, error, stackTrace) {
        return Icon(
          fallbackIcon,
          size: size,
          color: color,
        );
      },
    );
  }
}
