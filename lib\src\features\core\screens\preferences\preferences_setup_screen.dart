import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:login_flutter_app/src/constants/colors.dart';
import 'package:login_flutter_app/src/constants/sizes.dart';
import 'package:login_flutter_app/src/features/core/controllers/language_controller.dart';
import 'package:login_flutter_app/src/features/core/controllers/theme_controller.dart';
import 'package:login_flutter_app/src/features/core/controllers/user_preferences_controller.dart';
import 'package:login_flutter_app/src/features/core/screens/dashboard/spiritual_dashboard.dart';
import 'package:login_flutter_app/src/features/core/screens/notifications/notification_preferences_screen.dart';
import 'package:login_flutter_app/src/localization/app_localizations.dart';

class PreferencesSetupScreen extends StatelessWidget {
  const PreferencesSetupScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final preferencesController = Get.put(UserPreferencesController());
    final languageController = Get.find<LanguageController>();
    final themeController = Get.find<ThemeController>();
    final localizations = AppLocalizations.of(context)!;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: isDark
                ? [tSpiritualDeepOrange, tSpiritualMaroon]
                : [tSpiritualSaffron, tSpiritualOrange],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(tDefaultSpace),
            child: Column(
              children: [
                // Header
                _buildHeader(context, localizations),

                const SizedBox(height: 40),

                // Preferences Cards
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        // Language Preference Card
                        _buildLanguagePreferenceCard(
                          context,
                          languageController,
                          preferencesController,
                          localizations,
                          isDark,
                        ),

                        const SizedBox(height: 20),

                        // Haptic Feedback Preference Card
                        _buildHapticFeedbackPreferenceCard(
                          context,
                          languageController,
                          localizations,
                          isDark,
                        ),

                        const SizedBox(height: 20),

                        // Notification Preferences Card
                        _buildNotificationPreferencesCard(
                          context,
                          localizations,
                          isDark,
                        ),

                        const SizedBox(height: 20),

                        // Future preferences can be added here
                        // _buildThemePreferenceCard(...),
                      ],
                    ),
                  ),
                ),

                // Action Buttons
                _buildActionButtons(
                  context,
                  preferencesController,
                  localizations,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context, AppLocalizations localizations) {
    return Column(
      children: [
        // Welcome Icon
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: tWhiteColor.withOpacity(0.2),
            borderRadius: BorderRadius.circular(50),
          ),
          child: const Icon(
            Icons.settings,
            size: 50,
            color: tWhiteColor,
          ),
        ),

        const SizedBox(height: 20),

        // Title
        Text(
          localizations.welcome,
          style: Theme.of(context).textTheme.headlineLarge?.copyWith(
                color: tWhiteColor,
                fontWeight: FontWeight.bold,
              ),
        ),

        const SizedBox(height: 10),

        // Subtitle
        Text(
          'Let\'s set up your preferences for the best spiritual experience',
          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: tWhiteColor.withOpacity(0.9),
              ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildLanguagePreferenceCard(
    BuildContext context,
    LanguageController languageController,
    UserPreferencesController preferencesController,
    AppLocalizations localizations,
    bool isDark,
  ) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: isDark ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Card Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: tSpiritualSaffron.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.language,
                    color: tSpiritualSaffron,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        localizations.language,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: isDark ? Colors.white : Colors.black87,
                            ),
                      ),
                      Text(
                        'Choose your preferred language',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: isDark ? Colors.white70 : Colors.black54,
                            ),
                      ),
                    ],
                  ),
                ),
                Obx(() => preferencesController.isLanguageSet.value
                    ? Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 24,
                      )
                    : Icon(
                        Icons.radio_button_unchecked,
                        color: Colors.grey,
                        size: 24,
                      )),
              ],
            ),

            const SizedBox(height: 20),

            // Language Options
            ...languageController.languages.map((language) {
              return Obx(() => Container(
                    margin: const EdgeInsets.only(bottom: 12),
                    child: ListTile(
                      leading: Text(
                        language.flag,
                        style: const TextStyle(fontSize: 24),
                      ),
                      title: Text(
                        language.name,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                      subtitle: Text(
                        language.nativeName,
                        style: TextStyle(
                          color: isDark ? Colors.white70 : Colors.black54,
                        ),
                      ),
                      trailing: languageController.currentLanguage.code ==
                              language.code
                          ? Icon(
                              Icons.check_circle,
                              color: tSpiritualSaffron,
                            )
                          : null,
                      onTap: () {
                        languageController.changeLanguage(language.code);
                        preferencesController.markLanguageAsSet();
                      },
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      tileColor: languageController.currentLanguage.code ==
                              language.code
                          ? tSpiritualSaffron.withOpacity(0.1)
                          : null,
                    ),
                  ));
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildHapticFeedbackPreferenceCard(
    BuildContext context,
    LanguageController languageController,
    AppLocalizations localizations,
    bool isDark,
  ) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: isDark ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Card Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: tSpiritualSaffron.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.vibration,
                    color: tSpiritualSaffron,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Haptic Feedback',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Feel tactile response when writing RAM',
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? Colors.white70 : Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                Obx(() => Switch(
                      value: languageController.isHapticFeedbackEnabled.value,
                      onChanged: (value) {
                        languageController.setHapticFeedback(value);
                      },
                      activeColor: tSpiritualSaffron,
                      activeTrackColor: tSpiritualSaffron.withOpacity(0.3),
                    )),
              ],
            ),

            const SizedBox(height: 16),

            // Description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isDark
                    ? tSpiritualSaffron.withOpacity(0.1)
                    : tSpiritualSaffron.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: tSpiritualSaffron,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'When enabled, you\'ll feel a gentle vibration each time you write RAM, enhancing your spiritual practice experience.',
                      style: TextStyle(
                        fontSize: 12,
                        color: isDark ? Colors.white70 : Colors.black54,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationPreferencesCard(
    BuildContext context,
    AppLocalizations localizations,
    bool isDark,
  ) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: isDark ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Card Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: tSpiritualMaroon.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.notifications_active,
                    color: tSpiritualMaroon,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Notification Settings',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Customize your spiritual notifications',
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? Colors.white70 : Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: isDark ? Colors.white60 : Colors.black54,
                  size: 16,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Description
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isDark
                    ? tSpiritualMaroon.withOpacity(0.1)
                    : tSpiritualMaroon.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: tSpiritualMaroon,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Set up daily reminders, spiritual quotes, pooja timings, and more to enhance your spiritual journey.',
                      style: TextStyle(
                        fontSize: 12,
                        color: isDark ? Colors.white70 : Colors.black54,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Action Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Get.to(() => const NotificationPreferencesScreen());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: tSpiritualMaroon,
                  foregroundColor: tWhiteColor,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  elevation: 2,
                ),
                child: const Text(
                  'Configure Notifications',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(
    BuildContext context,
    UserPreferencesController preferencesController,
    AppLocalizations localizations,
  ) {
    return Column(
      children: [
        // Continue Button
        Obx(() => SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: preferencesController.areAllPreferencesSet
                    ? () {
                        preferencesController.completePreferencesSetup();
                        Get.offAll(() => const SpiritualDashboard());
                      }
                    : null,
                style: ElevatedButton.styleFrom(
                  backgroundColor: tWhiteColor,
                  foregroundColor: tSpiritualSaffron,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 4,
                ),
                child: Text(
                  'Continue to App',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            )),

        const SizedBox(height: 12),

        // Skip Button
        TextButton(
          onPressed: () {
            preferencesController.completePreferencesSetup();
            Get.offAll(() => const SpiritualDashboard());
          },
          child: Text(
            'Skip for now',
            style: TextStyle(
              color: tWhiteColor.withOpacity(0.8),
              fontSize: 14,
            ),
          ),
        ),
      ],
    );
  }
}
