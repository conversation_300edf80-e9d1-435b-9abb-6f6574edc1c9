import 'dart:convert';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:login_flutter_app/src/constants/colors.dart';

/// Notification categories for granular control
enum NotificationCategory {
  dailyReminders('daily_reminders', 'Daily Reminders',
      'Get reminded to practice Ram Naam <PERSON> daily'),
  spiritualQuotes('spiritual_quotes', 'Spiritual Quotes',
      'Receive inspiring spiritual quotes and teachings'),
  poojaTimings('pooja_timings', 'Pooja Timings',
      'Get notified about auspicious pooja times'),
  spiritualNews('spiritual_news', 'Spiritual News',
      'Stay updated with spiritual events and news'),
  festivalAlerts('festival_alerts', 'Festival Alerts',
      'Get notified about upcoming spiritual festivals'),
  ramNavami('ram_navami', 'Ram Navami Special',
      'Special notifications for Ram Navami celebrations'),
  weeklyProgress('weekly_progress', 'Weekly Progress',
      'Your weekly spiritual practice summary'),
  motivationalMessages('motivational_messages', 'Motivational Messages',
      'Encouraging messages for your spiritual journey');

  const NotificationCategory(this.key, this.title, this.description);
  final String key;
  final String title;
  final String description;
}

/// Firebase Cloud Messaging Service with granular notification preferences
class NotificationService extends GetxService {
  static NotificationService get instance => Get.find();

  late FirebaseMessaging _messaging;
  final _storage = GetStorage();

  // Storage keys
  static const String _fcmTokenKey = 'fcm_token';
  static const String _notificationPrefsKey = 'notification_preferences';
  static const String _masterNotificationKey = 'master_notifications_enabled';

  // Reactive variables
  RxBool isMasterNotificationEnabled = true.obs;
  RxMap<String, bool> notificationPreferences = <String, bool>{}.obs;
  RxString fcmToken = ''.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeFirebaseMessaging();
    _loadNotificationPreferences();
  }

  /// Initialize Firebase Cloud Messaging
  Future<void> _initializeFirebaseMessaging() async {
    try {
      _messaging = FirebaseMessaging.instance;

      // Request permission for notifications
      await _requestNotificationPermissions();

      // Get FCM token
      await _getFCMToken();

      // Set up message handlers
      _setupMessageHandlers();

      // Subscribe to default topics based on preferences
      await _subscribeToTopics();

      print('Firebase Cloud Messaging initialized successfully');
    } catch (e) {
      print('Error initializing Firebase Cloud Messaging: $e');
    }
  }

  /// Request notification permissions
  Future<void> _requestNotificationPermissions() async {
    try {
      NotificationSettings settings = await _messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('User granted notification permissions');
      } else if (settings.authorizationStatus ==
          AuthorizationStatus.provisional) {
        print('User granted provisional notification permissions');
      } else {
        print('User declined or has not accepted notification permissions');
        // Disable master notifications if permission denied
        await setMasterNotificationEnabled(false);
      }
    } catch (e) {
      print('Error requesting notification permissions: $e');
    }
  }

  /// Get FCM token for this device
  Future<void> _getFCMToken() async {
    try {
      String? token = await _messaging.getToken();
      if (token != null) {
        fcmToken.value = token;
        await _storage.write(_fcmTokenKey, token);
        print('FCM Token: $token');

        // Send token to your server here if needed
        // await _sendTokenToServer(token);
      }
    } catch (e) {
      print('Error getting FCM token: $e');
    }
  }

  /// Setup message handlers for different app states
  void _setupMessageHandlers() {
    // Handle messages when app is in foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _handleForegroundMessage(message);
    });

    // Handle messages when app is opened from background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _handleMessageOpenedApp(message);
    });

    // Handle messages when app is opened from terminated state
    _messaging.getInitialMessage().then((RemoteMessage? message) {
      if (message != null) {
        _handleMessageOpenedApp(message);
      }
    });
  }

  /// Handle messages when app is in foreground
  void _handleForegroundMessage(RemoteMessage message) {
    print('Received foreground message: ${message.messageId}');

    // Show in-app notification
    if (message.notification != null) {
      _showInAppNotification(message);
    }
  }

  /// Handle messages when app is opened from notification
  void _handleMessageOpenedApp(RemoteMessage message) {
    print('Message opened app: ${message.messageId}');

    // Navigate based on notification data
    _handleNotificationNavigation(message);
  }

  /// Show in-app notification when app is in foreground
  void _showInAppNotification(RemoteMessage message) {
    Get.snackbar(
      message.notification?.title ?? 'Notification',
      message.notification?.body ?? '',
      snackPosition: SnackPosition.TOP,
      backgroundColor: tSpiritualSaffron.withOpacity(0.9),
      colorText: tWhiteColor,
      duration: const Duration(seconds: 4),
      margin: const EdgeInsets.all(16),
      borderRadius: 12,
      icon: const Icon(Icons.notifications, color: tWhiteColor),
      shouldIconPulse: true,
      onTap: (_) => _handleNotificationNavigation(message),
    );
  }

  /// Handle navigation when notification is tapped
  void _handleNotificationNavigation(RemoteMessage message) {
    // Extract navigation data from message
    String? screen = message.data['screen'];
    String? action = message.data['action'];

    switch (screen) {
      case 'ram_naam_jaap':
        // Navigate to Ram Naam Jaap screen
        break;
      case 'spiritual_quotes':
        // Navigate to quotes screen
        break;
      case 'pooja_timings':
        // Navigate to pooja timings screen
        break;
      default:
        // Navigate to home screen
        break;
    }
  }

  /// Load notification preferences from storage
  void _loadNotificationPreferences() {
    try {
      // Load master notification setting
      bool masterEnabled = _storage.read(_masterNotificationKey) ?? true;
      isMasterNotificationEnabled.value = masterEnabled;

      // Load individual category preferences
      Map<String, dynamic>? prefs = _storage.read(_notificationPrefsKey);
      if (prefs != null) {
        notificationPreferences.value = Map<String, bool>.from(prefs);
      } else {
        // Set default preferences (all enabled)
        _setDefaultNotificationPreferences();
      }
    } catch (e) {
      print('Error loading notification preferences: $e');
      _setDefaultNotificationPreferences();
    }
  }

  /// Set default notification preferences
  void _setDefaultNotificationPreferences() {
    Map<String, bool> defaultPrefs = {};
    for (NotificationCategory category in NotificationCategory.values) {
      defaultPrefs[category.key] = true;
    }
    notificationPreferences.value = defaultPrefs;
    _saveNotificationPreferences();
  }

  /// Save notification preferences to storage
  Future<void> _saveNotificationPreferences() async {
    try {
      await _storage.write(
          _notificationPrefsKey, notificationPreferences.value);
    } catch (e) {
      print('Error saving notification preferences: $e');
    }
  }

  /// Subscribe to FCM topics based on preferences
  Future<void> _subscribeToTopics() async {
    try {
      if (!isMasterNotificationEnabled.value) return;

      for (NotificationCategory category in NotificationCategory.values) {
        bool isEnabled = notificationPreferences[category.key] ?? true;
        if (isEnabled) {
          await _messaging.subscribeToTopic(category.key);
          print('Subscribed to topic: ${category.key}');
        } else {
          await _messaging.unsubscribeFromTopic(category.key);
          print('Unsubscribed from topic: ${category.key}');
        }
      }
    } catch (e) {
      print('Error managing topic subscriptions: $e');
    }
  }

  /// Enable/disable master notifications
  Future<void> setMasterNotificationEnabled(bool enabled) async {
    try {
      isMasterNotificationEnabled.value = enabled;
      await _storage.write(_masterNotificationKey, enabled);

      if (enabled) {
        // Re-subscribe to enabled topics
        await _subscribeToTopics();
      } else {
        // Unsubscribe from all topics
        for (NotificationCategory category in NotificationCategory.values) {
          await _messaging.unsubscribeFromTopic(category.key);
        }
      }

      print('Master notifications ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      print('Error setting master notification preference: $e');
    }
  }

  /// Enable/disable specific notification category
  Future<void> setCategoryEnabled(
      NotificationCategory category, bool enabled) async {
    try {
      notificationPreferences[category.key] = enabled;
      await _saveNotificationPreferences();

      if (isMasterNotificationEnabled.value) {
        if (enabled) {
          await _messaging.subscribeToTopic(category.key);
          print('Subscribed to ${category.key}');
        } else {
          await _messaging.unsubscribeFromTopic(category.key);
          print('Unsubscribed from ${category.key}');
        }
      }
    } catch (e) {
      print('Error setting category preference: $e');
    }
  }

  /// Check if a specific category is enabled
  bool isCategoryEnabled(NotificationCategory category) {
    return notificationPreferences[category.key] ?? true;
  }

  /// Get all notification categories
  List<NotificationCategory> getAllCategories() {
    return NotificationCategory.values;
  }

  /// Send token to server (implement based on your backend)
  Future<void> _sendTokenToServer(String token) async {
    try {
      // Implement your server API call here
      // Example:
      // await http.post(
      //   Uri.parse('https://your-server.com/api/fcm-token'),
      //   headers: {'Content-Type': 'application/json'},
      //   body: jsonEncode({'token': token, 'userId': currentUserId}),
      // );
      print('Token sent to server: $token');
    } catch (e) {
      print('Error sending token to server: $e');
    }
  }

  /// Refresh FCM token
  Future<void> refreshToken() async {
    await _getFCMToken();
  }

  /// Check if notifications are available
  bool get isAvailable {
    try {
      return _messaging != null;
    } catch (e) {
      return false;
    }
  }
}

/// Background message handler (must be top-level function)
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('Handling background message: ${message.messageId}');
  // Handle background message here
}
