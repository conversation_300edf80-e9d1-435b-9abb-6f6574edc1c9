import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:login_flutter_app/src/constants/colors.dart';
import 'package:login_flutter_app/src/features/core/controllers/theme_controller.dart';
import 'package:login_flutter_app/src/services/notification_service.dart';

class NotificationPreferencesScreen extends StatelessWidget {
  const NotificationPreferencesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final notificationService = Get.find<NotificationService>();
    final themeController = Get.find<ThemeController>();

    return Obx(() {
      final isDark = themeController.isDarkMode.value;
      
      return Scaffold(
        backgroundColor: isDark ? tDarkColor : tWhiteColor,
        appBar: AppBar(
          backgroundColor: isDark ? tDarkColor : tWhiteColor,
          elevation: 0,
          leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: isDark ? tWhiteColor : tDarkColor,
            ),
            onPressed: () => Get.back(),
          ),
          title: Text(
            'Notification Settings',
            style: TextStyle(
              color: isDark ? tWhiteColor : tDarkColor,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Section
              _buildHeaderSection(isDark),
              
              const SizedBox(height: 30),
              
              // Master Notification Toggle
              _buildMasterNotificationCard(notificationService, isDark),
              
              const SizedBox(height: 20),
              
              // Notification Categories
              _buildNotificationCategoriesSection(notificationService, isDark),
              
              const SizedBox(height: 30),
              
              // Additional Settings
              _buildAdditionalSettingsSection(isDark),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildHeaderSection(bool isDark) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            tSpiritualSaffron.withOpacity(0.1),
            tSpiritualOrange.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: tSpiritualSaffron.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.notifications_active,
              color: tSpiritualSaffron,
              size: 32,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Stay Connected',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: isDark ? tWhiteColor : tDarkColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Customize your spiritual journey with personalized notifications',
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark ? tWhiteColor.withOpacity(0.7) : tDarkColor.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMasterNotificationCard(NotificationService service, bool isDark) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: isDark ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: tSpiritualMaroon.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.notifications,
                    color: tSpiritualMaroon,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'All Notifications',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Master control for all notification types',
                        style: TextStyle(
                          fontSize: 14,
                          color: isDark ? Colors.white70 : Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                Obx(() => Switch(
                  value: service.isMasterNotificationEnabled.value,
                  onChanged: (value) async {
                    await service.setMasterNotificationEnabled(value);
                    Get.snackbar(
                      'Notifications',
                      value ? 'All notifications enabled' : 'All notifications disabled',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: tSpiritualSaffron.withOpacity(0.8),
                      colorText: tWhiteColor,
                      duration: const Duration(seconds: 2),
                    );
                  },
                  activeColor: tSpiritualSaffron,
                  activeTrackColor: tSpiritualSaffron.withOpacity(0.3),
                )),
              ],
            ),
            
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isDark 
                    ? tSpiritualMaroon.withOpacity(0.1)
                    : tSpiritualMaroon.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: tSpiritualMaroon,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'When disabled, you won\'t receive any notifications from the app.',
                      style: TextStyle(
                        fontSize: 12,
                        color: isDark ? Colors.white70 : Colors.black54,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationCategoriesSection(NotificationService service, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notification Categories',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: isDark ? tWhiteColor : tDarkColor,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Choose which types of notifications you want to receive',
          style: TextStyle(
            fontSize: 14,
            color: isDark ? tWhiteColor.withOpacity(0.7) : tDarkColor.withOpacity(0.7),
          ),
        ),
        const SizedBox(height: 16),
        
        // Notification category cards
        ...service.getAllCategories().map((category) => 
          _buildNotificationCategoryCard(service, category, isDark)
        ).toList(),
      ],
    );
  }

  Widget _buildNotificationCategoryCard(
    NotificationService service, 
    NotificationCategory category, 
    bool isDark
  ) {
    IconData categoryIcon = _getCategoryIcon(category);
    Color categoryColor = _getCategoryColor(category);
    
    return Obx(() {
      bool isEnabled = service.isCategoryEnabled(category);
      bool isMasterEnabled = service.isMasterNotificationEnabled.value;
      
      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        child: Card(
          elevation: 4,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          color: isDark ? Colors.grey[800] : Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: categoryColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    categoryIcon,
                    color: categoryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.title,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isDark ? Colors.white : Colors.black87,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        category.description,
                        style: TextStyle(
                          fontSize: 12,
                          color: isDark ? Colors.white60 : Colors.black54,
                        ),
                      ),
                    ],
                  ),
                ),
                Switch(
                  value: isEnabled && isMasterEnabled,
                  onChanged: isMasterEnabled ? (value) async {
                    await service.setCategoryEnabled(category, value);
                    Get.snackbar(
                      category.title,
                      value ? 'Enabled' : 'Disabled',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: categoryColor.withOpacity(0.8),
                      colorText: tWhiteColor,
                      duration: const Duration(seconds: 1),
                    );
                  } : null,
                  activeColor: categoryColor,
                  activeTrackColor: categoryColor.withOpacity(0.3),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildAdditionalSettingsSection(bool isDark) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: isDark ? Colors.grey[800] : Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Additional Settings',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: isDark ? Colors.white : Colors.black87,
              ),
            ),
            const SizedBox(height: 12),
            
            ListTile(
              leading: Icon(
                Icons.schedule,
                color: tSpiritualOrange,
              ),
              title: Text(
                'Quiet Hours',
                style: TextStyle(
                  color: isDark ? Colors.white : Colors.black87,
                ),
              ),
              subtitle: Text(
                'Set times when notifications are muted',
                style: TextStyle(
                  color: isDark ? Colors.white60 : Colors.black54,
                  fontSize: 12,
                ),
              ),
              trailing: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color: isDark ? Colors.white60 : Colors.black54,
              ),
              onTap: () {
                // TODO: Implement quiet hours settings
                Get.snackbar(
                  'Coming Soon',
                  'Quiet hours feature will be available in the next update',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: tSpiritualSaffron.withOpacity(0.8),
                  colorText: tWhiteColor,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  IconData _getCategoryIcon(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.dailyReminders:
        return Icons.alarm;
      case NotificationCategory.spiritualQuotes:
        return Icons.format_quote;
      case NotificationCategory.poojaTimings:
        return Icons.access_time;
      case NotificationCategory.spiritualNews:
        return Icons.newspaper;
      case NotificationCategory.festivalAlerts:
        return Icons.celebration;
      case NotificationCategory.ramNavami:
        return Icons.temple_hindu;
      case NotificationCategory.weeklyProgress:
        return Icons.trending_up;
      case NotificationCategory.motivationalMessages:
        return Icons.favorite;
    }
  }

  Color _getCategoryColor(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.dailyReminders:
        return tSpiritualSaffron;
      case NotificationCategory.spiritualQuotes:
        return tSpiritualMaroon;
      case NotificationCategory.poojaTimings:
        return tSpiritualOrange;
      case NotificationCategory.spiritualNews:
        return Colors.blue;
      case NotificationCategory.festivalAlerts:
        return Colors.purple;
      case NotificationCategory.ramNavami:
        return tSpiritualSaffron;
      case NotificationCategory.weeklyProgress:
        return Colors.green;
      case NotificationCategory.motivationalMessages:
        return Colors.pink;
    }
  }
}
