import 'app_localizations.dart';

class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Ram Naam Jaap';

  // Welcome & Authentication
  @override
  String get welcome => 'Welcome';
  @override
  String get getStarted => 'Get Started';
  @override
  String get login => 'Login';
  @override
  String get signup => 'Sign Up';
  @override
  String get email => 'Email';
  @override
  String get password => 'Password';
  @override
  String get confirmPassword => 'Confirm Password';
  @override
  String get forgotPassword => 'Forgot Password?';
  @override
  String get rememberMe => 'Remember Me';
  @override
  String get dontHaveAccount => "Don't have an account?";
  @override
  String get alreadyHaveAccount => 'Already have an account?';
  @override
  String get createAccount => 'Create Account';
  @override
  String get or => 'OR';
  @override
  String get signInWithGoogle => 'Sign in with <PERSON>';
  @override
  String get signInWithFacebook => 'Sign in with Facebook';

  // Spiritual Dashboard
  @override
  String get spiritualGreeting => 'Om Namah Shivaya';
  @override
  String get spiritualSubGreeting => 'Begin your spiritual journey today';
  @override
  String get todaysMantra => "Today's Mantra";
  @override
  String get spiritualPractices => 'Spiritual Practices';
  @override
  String get ramNaamJaap => 'Ram Naam Jaap';
  @override
  String get meditation => 'Meditation';
  @override
  String get prayers => 'Prayers';
  @override
  String get temple => 'Temple';
  @override
  String get sacredTexts => 'Sacred Texts';
  @override
  String get spiritualCalendar => 'Spiritual Calendar';
  @override
  String get quickActions => 'Quick Actions';
  @override
  String get startJaap => 'Start Jaap';
  @override
  String get playMantras => 'Play Mantras';
  @override
  String get dailyPrayer => 'Daily Prayer';
  @override
  String get spiritualQuote => 'Spiritual Quote';
  @override
  String get blessings => 'Divine Blessings';
  @override
  String get auspiciousTime => 'Auspicious Time';
  @override
  String get festivals => 'Festivals';
  @override
  String get deities => 'Deities';
  @override
  String get ganesh => 'Ganesh';
  @override
  String get shiva => 'Shiva';
  @override
  String get krishna => 'Krishna';
  @override
  String get hanuman => 'Hanuman';
  @override
  String get durga => 'Durga';
  @override
  String get ram => 'Ram';

  // Ram Naam Jaap
  @override
  String get ramNaamJaapTitle => 'राम नाम जप';
  @override
  String get ramNaamJaapSubtitle => 'Ram Naam Jaap';
  @override
  String get writeRamName => 'Write the sacred name of Lord Ram 108 times';
  @override
  String get startWritingRam => 'Start Writing RAM';
  @override
  String get jaapCount => 'Jaap Count';
  @override
  String get ramMantra => 'Ram Mantra';
  @override
  String get ramChalisa => 'Ram Chalisa';
  @override
  String get ramTeaching => "Ram's Teaching";
  @override
  String get ramDevotion => 'Ram Devotion';
  @override
  String get todaysRamGuidance => "Today's Ram Guidance";

  // Common
  @override
  String get profile => 'Profile';
  @override
  String get settings => 'Settings';
  @override
  String get language => 'Language';
  @override
  String get theme => 'Theme';
  @override
  String get darkMode => 'Dark Mode';
  @override
  String get lightMode => 'Light Mode';
  @override
  String get save => 'Save';
  @override
  String get cancel => 'Cancel';
  @override
  String get ok => 'OK';
  @override
  String get yes => 'Yes';
  @override
  String get no => 'No';
  @override
  String get back => 'Back';
  @override
  String get next => 'Next';
  @override
  String get done => 'Done';
  @override
  String get loading => 'Loading...';
  @override
  String get error => 'Error';
  @override
  String get success => 'Success';

  // Mantras and Spiritual Content
  @override
  String get omNamahShivaya => 'Om Namah Shivaya';
  @override
  String get shrirRamJayRam => 'श्री राम जय राम';
  @override
  String get raghukulRiti => 'रघुकुल रीति सदा चली आई, प्राण जाय पर वचन न जाई';
  @override
  String get raghukulRitiTranslation => 'The tradition of Raghu\'s lineage has always been maintained - life may go, but never the word.';
  @override
  String get brahmuMuhurta => 'Brahma Muhurta';
  @override
  String get auspiciousTimeDesc => '4:00 AM - 6:00 AM';
  @override
  String get buddhaQuote => 'The mind is everything. What you think you become.';
  @override
  String get todayAuspicious => 'Today is auspicious for spiritual practices';

  // Other Practices
  @override
  String get otherSpiritualPractices => 'Other Spiritual Practices';
  @override
  String get innerPeace => 'Inner Peace';
  @override
  String get dailyPrayers => 'Daily Prayers';
  @override
  String get sacredPlaces => 'Sacred Places';
  @override
  String get fortyVerses => '40 Verses';
}
