import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

class FirebaseService extends GetxService {
  static FirebaseService get instance => Get.find();

  late FirebaseAnalytics _analytics;
  late FirebaseCrashlytics _crashlytics;

  FirebaseAnalytics get analytics => _analytics;
  FirebaseCrashlytics get crashlytics => _crashlytics;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeFirebaseServices();
  }

  /// Initialize Firebase Analytics and Crashlytics
  Future<void> _initializeFirebaseServices() async {
    try {
      // Initialize Analytics
      _analytics = FirebaseAnalytics.instance;

      // Initialize Crashlytics
      _crashlytics = FirebaseCrashlytics.instance;

      // Set up Crashlytics
      await _setupCrashlytics();

      // Set up Analytics
      await _setupAnalytics();

      print('Firebase services initialized successfully');
    } catch (e) {
      print('Error initializing Firebase services: $e');
    }
  }

  /// Setup Crashlytics configuration
  Future<void> _setupCrashlytics() async {
    // Enable Crashlytics collection
    await _crashlytics.setCrashlyticsCollectionEnabled(true);

    // Pass all uncaught "fatal" errors from the framework to Crashlytics
    FlutterError.onError = (errorDetails) {
      _crashlytics.recordFlutterFatalError(errorDetails);
    };

    // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
    PlatformDispatcher.instance.onError = (error, stack) {
      _crashlytics.recordError(error, stack, fatal: true);
      return true;
    };
  }

  /// Setup Analytics configuration
  Future<void> _setupAnalytics() async {
    // Enable Analytics collection
    await _analytics.setAnalyticsCollectionEnabled(true);

    // Set default parameters
    await _analytics.setDefaultEventParameters({
      'app_version': '1.0.0',
      'platform': GetPlatform.isAndroid ? 'android' : 'ios',
    });
  }

  // ANALYTICS METHODS

  /// Log screen view
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
    Map<String, Object>? parameters,
  }) async {
    try {
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass ?? screenName,
        parameters: parameters,
      );
    } catch (e) {
      print('Error logging screen view: $e');
    }
  }

  /// Log custom event
  Future<void> logEvent({
    required String name,
    Map<String, Object>? parameters,
  }) async {
    try {
      await _analytics.logEvent(
        name: name,
        parameters: parameters,
      );
    } catch (e) {
      print('Error logging event: $e');
    }
  }

  /// Log user login
  Future<void> logLogin({
    required String loginMethod,
    String? userId,
  }) async {
    try {
      await _analytics.logLogin(
        loginMethod: loginMethod,
        parameters: {
          'user_id': userId ?? 'unknown',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
    } catch (e) {
      print('Error logging login: $e');
    }
  }

  /// Log user signup
  Future<void> logSignUp({
    required String signUpMethod,
    String? userId,
  }) async {
    try {
      await _analytics.logSignUp(
        signUpMethod: signUpMethod,
        parameters: {
          'user_id': userId ?? 'unknown',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
    } catch (e) {
      print('Error logging signup: $e');
    }
  }

  /// Log spiritual practice events
  Future<void> logSpiritualPractice({
    required String practiceType,
    int? duration,
    Map<String, Object>? additionalParams,
  }) async {
    try {
      final parameters = <String, Object>{
        'practice_type': practiceType,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        if (duration != null) 'duration_seconds': duration,
        ...?additionalParams,
      };

      await _analytics.logEvent(
        name: 'spiritual_practice',
        parameters: parameters,
      );
    } catch (e) {
      print('Error logging spiritual practice: $e');
    }
  }

  /// Log Ram Naam Jaap session
  Future<void> logRamNaamJaapSession({
    required int boxesFilled,
    required int totalBoxes,
    required int sessionDuration,
    String? completionStatus,
  }) async {
    try {
      await _analytics.logEvent(
        name: 'ram_naam_jaap_session',
        parameters: {
          'boxes_filled': boxesFilled,
          'total_boxes': totalBoxes,
          'completion_percentage': (boxesFilled / totalBoxes * 100).round(),
          'session_duration_seconds': sessionDuration,
          'completion_status': completionStatus ?? 'incomplete',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        },
      );
    } catch (e) {
      print('Error logging Ram Naam Jaap session: $e');
    }
  }

  /// Set user properties
  Future<void> setUserProperties({
    String? userId,
    String? userType,
    String? preferredLanguage,
    String? spiritualLevel,
    Map<String, String>? customProperties,
  }) async {
    try {
      if (userId != null) {
        await _analytics.setUserId(id: userId);
      }

      final properties = <String, String>{
        if (userType != null) 'user_type': userType,
        if (preferredLanguage != null) 'preferred_language': preferredLanguage,
        if (spiritualLevel != null) 'spiritual_level': spiritualLevel,
        ...?customProperties,
      };

      for (final entry in properties.entries) {
        await _analytics.setUserProperty(
          name: entry.key,
          value: entry.value,
        );
      }
    } catch (e) {
      print('Error setting user properties: $e');
    }
  }

  // CRASHLYTICS METHODS

  /// Log non-fatal error
  Future<void> logError({
    required dynamic error,
    StackTrace? stackTrace,
    String? reason,
    bool fatal = false,
    Map<String, String>? customKeys,
  }) async {
    try {
      // Set custom keys if provided
      if (customKeys != null) {
        for (final entry in customKeys.entries) {
          await _crashlytics.setCustomKey(entry.key, entry.value);
        }
      }

      await _crashlytics.recordError(
        error,
        stackTrace,
        reason: reason,
        fatal: fatal,
      );
    } catch (e) {
      print('Error logging to Crashlytics: $e');
    }
  }

  /// Set user identifier for crash reports
  Future<void> setCrashlyticsUserId(String userId) async {
    try {
      await _crashlytics.setUserIdentifier(userId);
    } catch (e) {
      print('Error setting Crashlytics user ID: $e');
    }
  }

  /// Add custom key-value pairs to crash reports
  Future<void> setCrashlyticsCustomKey(String key, String value) async {
    try {
      await _crashlytics.setCustomKey(key, value);
    } catch (e) {
      print('Error setting Crashlytics custom key: $e');
    }
  }

  /// Log a message to Crashlytics
  Future<void> logMessage(String message) async {
    try {
      await _crashlytics.log(message);
    } catch (e) {
      print('Error logging message to Crashlytics: $e');
    }
  }

  /// Force a crash for testing (DEBUG ONLY)
  void forceCrash() {
    if (kDebugMode) {
      _crashlytics.crash();
    }
  }

  // UTILITY METHODS

  /// Check if Firebase services are available
  bool get isAvailable {
    try {
      return _analytics != null && _crashlytics != null;
    } catch (e) {
      return false;
    }
  }

  /// Get Analytics observer for navigation tracking
  FirebaseAnalyticsObserver getAnalyticsObserver() {
    return FirebaseAnalyticsObserver(analytics: _analytics);
  }
}
