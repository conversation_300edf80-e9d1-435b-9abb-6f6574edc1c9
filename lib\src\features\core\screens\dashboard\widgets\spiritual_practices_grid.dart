import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../constants/colors.dart';
import '../../../../../constants/image_strings.dart';
import '../../../../../constants/text_strings.dart';
import '../../../../writer/screens/writer_108_box/writer_108_box_screen.dart';

class SpiritualPracticesGrid extends StatelessWidget {
  const SpiritualPracticesGrid({
    Key? key,
    required this.txtTheme,
    required this.isDark,
  }) : super(key: key);

  final TextTheme txtTheme;
  final bool isDark;

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 15,
      mainAxisSpacing: 15,
      childAspectRatio: 1.1,
      children: [
        _buildPracticeCard(
          title: tRamNaamJaap,
          subtitle: "108 Names",
          icon: tRamSymbol,
          fallbackIcon: Icons.edit,
          colors: isDark
              ? [tSpiritualMaroon, tSpiritualDeepOrange]
              : [tSpiritualSaffron, tSpiritualOrange],
          onTap: () => Get.to(() => const Writer108BoxScreen()),
        ),
        _buildPracticeCard(
          title: tMeditation,
          subtitle: "Inner Peace",
          icon: tMeditationIcon,
          fallbackIcon: Icons.self_improvement,
          colors: isDark
              ? [tSpiritualDeepOrange, tSpiritualOrange]
              : [tSpiritualLotus, tSpiritualSaffron],
          onTap: () {
            // Navigate to meditation screen
          },
        ),
        _buildPracticeCard(
          title: tPrayers,
          subtitle: "Daily Prayers",
          icon: tPrayerHands,
          fallbackIcon: Icons.favorite,
          colors: isDark
              ? [tSpiritualOrange, tSpiritualGold]
              : [tSpiritualGold, tSpiritualYellow],
          onTap: () {
            // Navigate to prayers screen
          },
        ),
        _buildPracticeCard(
          title: tTempleVisit,
          subtitle: "Sacred Places",
          icon: tTempleIcon,
          fallbackIcon: Icons.temple_hindu,
          colors: isDark
              ? [tSpiritualGold, tSpiritualYellow]
              : [tSpiritualBrown, tSpiritualMaroon],
          onTap: () {
            // Navigate to temple screen
          },
        ),
      ],
    );
  }

  Widget _buildPracticeCard({
    required String title,
    required String subtitle,
    required String icon,
    required IconData fallbackIcon,
    required List<Color> colors,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: LinearGradient(
            colors: colors.map((color) => color.withOpacity(0.8)).toList(),
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: colors.first.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: tWhiteColor.withOpacity(0.2),
              ),
              child: Image.asset(
                icon,
                width: 35,
                height: 35,
                color: tWhiteColor,
                errorBuilder: (context, error, stackTrace) {
                  return Icon(
                    fallbackIcon,
                    size: 35,
                    color: tWhiteColor,
                  );
                },
              ),
            ),
            const SizedBox(height: 15),
            Text(
              title,
              style: TextStyle(
                color: tWhiteColor,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 5),
            Text(
              subtitle,
              style: TextStyle(
                color: tWhiteColor.withOpacity(0.8),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
