# Quick Implementation Guide - FCM Notification System

## 🚀 Quick Start (5 Minutes)

### Step 1: Add Dependencies
```yaml
# pubspec.yaml
dependencies:
  firebase_core: ^2.24.0
  firebase_messaging: ^14.7.0
  get: ^4.6.6
  get_storage: ^2.1.1
```

### Step 2: Initialize in main.dart
```dart
// main.dart
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:login_flutter_app/src/services/notification_service.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform)
      .then((_) async {
    // Set up background message handler
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);
    
    // Initialize services
    await Get.putAsync(() => FirebaseService().onInit().then((_) => FirebaseService()));
    await Get.putAsync(() => NotificationService().onInit().then((_) => NotificationService()));
    
    Get.put(AuthenticationRepository());
  });
  
  runApp(const App());
}

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  print('Handling background message: ${message.messageId}');
}
```

### Step 3: Copy Core Files
1. Copy `lib/src/services/notification_service.dart`
2. Copy `lib/src/features/core/screens/notifications/notification_preferences_screen.dart`

### Step 4: Add Navigation
```dart
// In your navigation drawer or settings
_buildDrawerItem(Icons.notifications, 'Notifications', isDark, () {
  Get.back();
  Get.to(() => const NotificationPreferencesScreen());
}),
```

### Step 5: Test
```bash
flutter run
# Check console for: "Firebase Cloud Messaging initialized successfully"
# Check console for: "Subscribed to topic: [category_name]"
```

## 🎯 Key Components

### 1. NotificationService (Core)
```dart
// Key methods you'll use:
final notificationService = Get.find<NotificationService>();

// Enable/disable master notifications
await notificationService.setMasterNotificationEnabled(true);

// Enable/disable specific category
await notificationService.setCategoryEnabled(
  NotificationCategory.dailyReminders, 
  true
);

// Check if category is enabled
bool isEnabled = notificationService.isCategoryEnabled(
  NotificationCategory.dailyReminders
);
```

### 2. Notification Categories
```dart
enum NotificationCategory {
  dailyReminders,      // Daily practice reminders
  spiritualQuotes,     // Inspirational quotes
  poojaTimings,        // Worship time notifications
  spiritualNews,       // Spiritual events and news
  festivalAlerts,      // Festival notifications
  ramNavami,           // Ram Navami special
  weeklyProgress,      // Weekly summaries
  motivationalMessages // Motivational content
}
```

### 3. UI Integration
```dart
// Master toggle
Obx(() => Switch(
  value: notificationService.isMasterNotificationEnabled.value,
  onChanged: (value) => notificationService.setMasterNotificationEnabled(value),
))

// Category toggle
Obx(() => Switch(
  value: notificationService.isCategoryEnabled(category),
  onChanged: (value) => notificationService.setCategoryEnabled(category, value),
))
```

## 📱 Testing Your Implementation

### 1. Local Testing
```dart
// Check if service is working
final service = Get.find<NotificationService>();
print('FCM Available: ${service.isAvailable}');
print('FCM Token: ${service.fcmToken.value}');
print('Master Enabled: ${service.isMasterNotificationEnabled.value}');
```

### 2. Firebase Console Testing
1. Go to Firebase Console → Cloud Messaging
2. Create new campaign
3. Target: Topic → Select `daily_reminders`
4. Send test message

### 3. Verification Checklist
- [ ] App requests notification permissions
- [ ] FCM token generated (check console logs)
- [ ] Topics subscribed (check console logs)
- [ ] Settings UI accessible
- [ ] Toggles work correctly
- [ ] Settings persist after app restart

## 🔧 Customization

### Add New Notification Category
```dart
// 1. Add to enum
enum NotificationCategory {
  // existing categories...
  newCategory('new_category', 'New Category', 'Description');
}

// 2. Add icon and color
IconData _getCategoryIcon(NotificationCategory category) {
  switch (category) {
    case NotificationCategory.newCategory:
      return Icons.new_icon;
    // other cases...
  }
}

Color _getCategoryColor(NotificationCategory category) {
  switch (category) {
    case NotificationCategory.newCategory:
      return Colors.blue;
    // other cases...
  }
}
```

### Modify UI Theme
```dart
// Change colors in notification_preferences_screen.dart
Container(
  decoration: BoxDecoration(
    color: YOUR_PRIMARY_COLOR.withOpacity(0.2), // Change this
    borderRadius: BorderRadius.circular(12),
  ),
  child: Icon(
    Icons.notifications_active,
    color: YOUR_PRIMARY_COLOR, // Change this
    size: 24,
  ),
),
```

### Custom Message Handling
```dart
// In notification_service.dart
void _handleNotificationNavigation(RemoteMessage message) {
  String? screen = message.data['screen'];
  
  switch (screen) {
    case 'custom_screen':
      Get.to(() => const YourCustomScreen());
      break;
    // Add more cases...
  }
}
```

## 🚨 Common Issues & Solutions

### Issue: Notifications not received
**Solution:**
```dart
// Check permissions
final service = Get.find<NotificationService>();
if (!service.isAvailable) {
  print('FCM not available - check Firebase setup');
}

// Check topic subscription
print('Subscribed topics: ${service.notificationPreferences.value}');
```

### Issue: App crashes on notification
**Solution:**
```dart
// Ensure background handler is set up correctly
@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Don't use Get.find() here - service might not be initialized
  print('Background message: ${message.messageId}');
}
```

### Issue: Settings not persisting
**Solution:**
```dart
// Ensure GetStorage is initialized in main.dart
await GetStorage.init();

// Check storage in notification service
final storage = GetStorage();
print('Stored preferences: ${storage.read('notification_preferences')}');
```

## 📤 Sending Notifications

### From Firebase Console
1. Cloud Messaging → New Campaign
2. Notification title/body
3. Target: Topic
4. Select topic (e.g., `daily_reminders`)
5. Send

### From Backend (Node.js)
```javascript
const admin = require('firebase-admin');

const message = {
  notification: {
    title: 'Daily Reminder',
    body: 'Time for your spiritual practice!'
  },
  data: {
    screen: 'ram_naam_jaap',
    action: 'open_writer'
  },
  topic: 'daily_reminders'
};

admin.messaging().send(message);
```

### From Backend (Python)
```python
from firebase_admin import messaging

message = messaging.Message(
    notification=messaging.Notification(
        title='Daily Reminder',
        body='Time for your spiritual practice!'
    ),
    data={
        'screen': 'ram_naam_jaap',
        'action': 'open_writer'
    },
    topic='daily_reminders'
)

response = messaging.send(message)
```

## 🔍 Debugging

### Enable Debug Logging
```dart
// In notification_service.dart, add debug prints
print('FCM Token: $token');
print('Subscribing to topic: ${category.key}');
print('Master notifications: ${enabled ? 'enabled' : 'disabled'}');
```

### Check Firebase Console
1. Project Settings → Cloud Messaging
2. Check if messages are being sent
3. View delivery reports

### Device Logs
```bash
# Android
adb logcat | grep flutter

# iOS
flutter logs
```

## 📋 Production Checklist

- [ ] Firebase project configured for production
- [ ] FCM server key secured
- [ ] All notification categories tested
- [ ] Permission flows tested on different devices
- [ ] Background message handling verified
- [ ] Error handling implemented
- [ ] Analytics events added (optional)
- [ ] Performance monitoring in place (optional)

## 🆘 Quick Help

**Need help?** Check these in order:
1. Console logs for error messages
2. Firebase Console for delivery status
3. Device notification settings
4. Network connectivity
5. Firebase project configuration

**Still stuck?** 
- Review the full `NOTIFICATION_SYSTEM_README.md`
- Check Firebase documentation
- Test with a fresh Firebase project

---

**Quick Start Time**: ~5 minutes
**Full Implementation**: ~30 minutes
**Testing & Verification**: ~15 minutes
