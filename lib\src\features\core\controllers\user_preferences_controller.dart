import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'language_controller.dart';
import 'theme_controller.dart';

class UserPreferencesController extends GetxController {
  static UserPreferencesController get instance => Get.find();

  final _box = GetStorage();
  final _preferencesSetupKey = 'preferences_setup_completed';
  final _languageSetKey = 'language_preference_set';
  final _themeSetKey = 'theme_preference_set';

  /// Reactive variables for preferences status
  RxBool isPreferencesSetupCompleted = false.obs;
  RxBool isLanguageSet = false.obs;
  RxBool isThemeSet = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadPreferencesStatus();
  }

  /// Load preferences status from storage
  void _loadPreferencesStatus() {
    isPreferencesSetupCompleted.value = _box.read(_preferencesSetupKey) ?? false;
    isLanguageSet.value = _box.read(_languageSetKey) ?? false;
    isThemeSet.value = _box.read(_themeSetKey) ?? false;
  }

  /// Check if all required preferences are set
  bool get areAllPreferencesSet {
    return isLanguageSet.value; // Add more preferences here as needed
  }

  /// Check if user needs to go through preferences setup
  bool get needsPreferencesSetup {
    return !isPreferencesSetupCompleted.value || !areAllPreferencesSet;
  }

  /// Mark language preference as set
  void markLanguageAsSet() {
    isLanguageSet.value = true;
    _box.write(_languageSetKey, true);
    _checkAndMarkSetupComplete();
  }

  /// Mark theme preference as set
  void markThemeAsSet() {
    isThemeSet.value = true;
    _box.write(_themeSetKey, true);
    _checkAndMarkSetupComplete();
  }

  /// Check if all preferences are set and mark setup as complete
  void _checkAndMarkSetupComplete() {
    if (areAllPreferencesSet) {
      isPreferencesSetupCompleted.value = true;
      _box.write(_preferencesSetupKey, true);
    }
  }

  /// Force complete preferences setup (for skip functionality)
  void completePreferencesSetup() {
    isPreferencesSetupCompleted.value = true;
    _box.write(_preferencesSetupKey, true);
    
    // Set default preferences if not already set
    if (!isLanguageSet.value) {
      markLanguageAsSet();
    }
  }

  /// Reset preferences setup (for testing or user reset)
  void resetPreferencesSetup() {
    isPreferencesSetupCompleted.value = false;
    isLanguageSet.value = false;
    isThemeSet.value = false;
    
    _box.remove(_preferencesSetupKey);
    _box.remove(_languageSetKey);
    _box.remove(_themeSetKey);
  }

  /// Get preferences completion status for UI
  Map<String, bool> get preferencesStatus {
    return {
      'language': isLanguageSet.value,
      'theme': isThemeSet.value,
      'overall': isPreferencesSetupCompleted.value,
    };
  }

  /// Get list of pending preferences
  List<String> get pendingPreferences {
    List<String> pending = [];
    
    if (!isLanguageSet.value) {
      pending.add('language');
    }
    
    return pending;
  }

  /// Initialize preferences with current controller states
  void initializeFromCurrentState() {
    final languageController = Get.find<LanguageController>();
    final themeController = Get.find<ThemeController>();
    
    // If controllers have been used, mark as set
    if (languageController.currentLocale.value.languageCode != 'en' || 
        _box.read('selectedLanguage') != null) {
      markLanguageAsSet();
    }
    
    if (_box.read('isDarkMode') != null) {
      markThemeAsSet();
    }
  }
}
