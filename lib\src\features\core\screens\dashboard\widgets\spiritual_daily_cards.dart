import 'package:flutter/material.dart';
import '../../../../../constants/colors.dart';
import '../../../../../constants/image_strings.dart';
import '../../../../../constants/text_strings.dart';

class SpiritualDailyCards extends StatelessWidget {
  const SpiritualDailyCards({
    Key? key,
    required this.txtTheme,
    required this.isDark,
  }) : super(key: key);

  final TextTheme txtTheme;
  final bool isDark;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Today's Spiritual Guidance",
          style: txtTheme.headlineMedium?.apply(
            color: isDark ? tWhiteColor : tSpiritualMaroon,
            fontWeightDelta: 2,
          ),
        ),
        const SizedBox(height: 15),
        SizedBox(
          height: 160,
          child: <PERSON>View(
            scrollDirection: Axis.horizontal,
            children: [
              _buildDailyCard(
                title: tTodaysMantra,
                content: "ॐ नमः शिवाय",
                subtitle: "Om Namah Shivaya",
                icon: tOmSymbol,
                colors: isDark 
                  ? [tSpiritualMaroon, tSpiritualDeepOrange]
                  : [tSpiritualSaffron, tSpiritualOrange],
              ),
              const SizedBox(width: 15),
              _buildDailyCard(
                title: tSpiritualQuote,
                content: "The mind is everything. What you think you become.",
                subtitle: "- Buddha",
                icon: tLotusBackground,
                colors: isDark 
                  ? [tSpiritualDeepOrange, tSpiritualOrange]
                  : [tSpiritualLotus, tSpiritualSaffron],
              ),
              const SizedBox(width: 15),
              _buildDailyCard(
                title: tAuspiciousTime,
                content: "Brahma Muhurta",
                subtitle: "4:00 AM - 6:00 AM",
                icon: tTempleIcon,
                colors: isDark 
                  ? [tSpiritualOrange, tSpiritualGold]
                  : [tSpiritualGold, tSpiritualYellow],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDailyCard({
    required String title,
    required String content,
    required String subtitle,
    required String icon,
    required List<Color> colors,
  }) {
    return Container(
      width: 280,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: colors.map((color) => color.withOpacity(0.8)).toList(),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: colors.first.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: tWhiteColor.withOpacity(0.2),
                ),
                child: Image.asset(
                  icon,
                  width: 24,
                  height: 24,
                  color: tWhiteColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    color: tWhiteColor,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Text(
            content,
            style: TextStyle(
              color: tWhiteColor,
              fontSize: 18,
              fontWeight: FontWeight.w600,
              height: 1.3,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              color: tWhiteColor.withOpacity(0.8),
              fontSize: 14,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }
}
