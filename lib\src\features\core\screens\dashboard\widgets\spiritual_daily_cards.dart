import 'package:flutter/material.dart';
import '../../../../../constants/colors.dart';
import '../../../../../constants/image_strings.dart';
import '../../../../../constants/text_strings.dart';

class SpiritualDailyCards extends StatelessWidget {
  const SpiritualDailyCards({
    Key? key,
    required this.txtTheme,
    required this.isDark,
  }) : super(key: key);

  final TextTheme txtTheme;
  final bool isDark;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Today's Spiritual Guidance",
          style: txtTheme.headlineMedium?.apply(
            color: isDark ? tWhiteColor : tSpiritualMaroon,
            fontWeightDelta: 2,
          ),
        ),
        const SizedBox(height: 15),
        SizedBox(
          height: 180,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              _buildDailyCard(
                title: tTodaysMantra,
                content: "ॐ नमः शिवाय",
                subtitle: "Om Namah Shivaya",
                icon: tOmSymbol,
                fallbackIcon: Icons.self_improvement,
                colors: isDark
                    ? [tSpiritualMaroon, tSpiritualDeepOrange]
                    : [tSpiritualSaffron, tSpiritualOrange],
              ),
              const SizedBox(width: 15),
              _buildDailyCard(
                title: tSpiritualQuote,
                content: "The mind is everything. What you think you become.",
                subtitle: "- Buddha",
                icon: tLotusBackground,
                fallbackIcon: Icons.format_quote,
                colors: isDark
                    ? [tSpiritualDeepOrange, tSpiritualOrange]
                    : [tSpiritualLotus, tSpiritualSaffron],
              ),
              const SizedBox(width: 15),
              _buildDailyCard(
                title: tAuspiciousTime,
                content: "Brahma Muhurta",
                subtitle: "4:00 AM - 6:00 AM",
                icon: tTempleIcon,
                fallbackIcon: Icons.access_time,
                colors: isDark
                    ? [tSpiritualOrange, tSpiritualGold]
                    : [tSpiritualGold, tSpiritualYellow],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDailyCard({
    required String title,
    required String content,
    required String subtitle,
    required String icon,
    required IconData fallbackIcon,
    required List<Color> colors,
  }) {
    return Container(
      width: 260,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: colors.map((color) => color.withOpacity(0.8)).toList(),
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: colors.first.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: tWhiteColor.withOpacity(0.2),
                ),
                child: Image.asset(
                  icon,
                  width: 20,
                  height: 20,
                  color: tWhiteColor,
                  errorBuilder: (context, error, stackTrace) {
                    return Icon(
                      fallbackIcon,
                      size: 20,
                      color: tWhiteColor,
                    );
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    color: tWhiteColor,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Expanded(
            child: Text(
              content,
              style: const TextStyle(
                color: tWhiteColor,
                fontSize: 15,
                fontWeight: FontWeight.w600,
                height: 1.2,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: TextStyle(
              color: tWhiteColor.withOpacity(0.8),
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
