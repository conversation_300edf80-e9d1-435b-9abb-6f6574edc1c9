import 'package:flutter/material.dart';
import '../../../../../constants/colors.dart';
import '../../../../../constants/image_strings.dart';
import '../../../../../constants/text_strings.dart';

class SpiritualGreeting extends StatelessWidget {
  const SpiritualGreeting({
    Key? key,
    required this.txtTheme,
    required this.isDark,
  }) : super(key: key);

  final TextTheme txtTheme;
  final bool isDark;

  @override
  Widget build(BuildContext context) {
    final now = DateTime.now();
    final hour = now.hour;
    
    String greeting;
    if (hour < 12) {
      greeting = "Good Morning";
    } else if (hour < 17) {
      greeting = "Good Afternoon";
    } else {
      greeting = "Good Evening";
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: isDark 
            ? [tSpiritualMaroon.withOpacity(0.7), tSpiritualDeepOrange.withOpacity(0.7)]
            : [tSpiritualSaffron.withOpacity(0.8), tSpiritualOrange.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark ? tSpiritualMaroon.withOpacity(0.3) : tSpiritualOrange.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: tWhiteColor.withOpacity(0.2),
                ),
                child: Image.asset(
                  tOmSymbol,
                  width: 30,
                  height: 30,
                  color: tWhiteColor,
                ),
              ),
              const SizedBox(width: 15),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      greeting,
                      style: txtTheme.bodyLarge?.copyWith(
                        color: tWhiteColor.withOpacity(0.9),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      tSpiritualGreeting,
                      style: txtTheme.displayMedium?.copyWith(
                        color: tWhiteColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 22,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 15),
          Text(
            tSpiritualSubGreeting,
            style: txtTheme.bodyMedium?.copyWith(
              color: tWhiteColor.withOpacity(0.9),
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 10),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: tWhiteColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              "Today is auspicious for spiritual practices",
              style: txtTheme.bodySmall?.copyWith(
                color: tWhiteColor,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
