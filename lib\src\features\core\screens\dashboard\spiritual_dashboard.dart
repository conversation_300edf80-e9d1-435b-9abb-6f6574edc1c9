import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:login_flutter_app/src/constants/colors.dart';
import 'package:login_flutter_app/src/constants/image_strings.dart';
import 'package:login_flutter_app/src/constants/sizes.dart';
import 'package:login_flutter_app/src/features/core/screens/dashboard/widgets/spiritual_appbar.dart';
import 'package:login_flutter_app/src/features/writer/screens/writer_108_box/writer_108_box_screen.dart';
import 'package:login_flutter_app/src/localization/app_localizations.dart';

class SpiritualDashboard extends StatelessWidget {
  const SpiritualDashboard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    //Variables
    final txtTheme = Theme.of(context).textTheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final localizations = AppLocalizations.of(context)!;

    return SafeArea(
      child: Scaffold(
        backgroundColor: isDark ? tDarkColor : tSpiritualCream,
        appBar: SpiritualAppBar(isDark: isDark),
        drawer: _buildSpiritualDrawer(isDark),
        body: SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(tDashboardPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Ram Naam Jaap - Main Feature (Priority #1)
                _buildRamNaamJaapSection(txtTheme, isDark, localizations),
                const SizedBox(height: tDashboardPadding),

                // Ram Devotion Section (Priority #2)
                _buildRamDevotionSection(txtTheme, isDark, localizations),
                const SizedBox(height: tDashboardPadding),

                // Today's Ram Guidance (Priority #3)
                _buildTodaysRamGuidance(txtTheme, isDark, localizations),
                const SizedBox(height: tDashboardPadding),

                // Other Spiritual Practices (Secondary)
                Text(localizations.otherSpiritualPractices,
                    style: txtTheme.headlineMedium?.apply(
                        color: isDark ? tWhiteColor : tSpiritualMaroon,
                        fontWeightDelta: 1)),
                const SizedBox(height: 10),
                _buildOtherPracticesGrid(txtTheme, isDark, localizations),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSpiritualDrawer(bool isDark) {
    return Drawer(
      backgroundColor: isDark ? tSecondaryColor : tSpiritualCream,
      child: ListView(
        children: [
          UserAccountsDrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark
                    ? [tSpiritualMaroon, tSpiritualDeepOrange]
                    : [tSpiritualSaffron, tSpiritualOrange],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            currentAccountPicture: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: tWhiteColor, width: 2),
              ),
              child: const CircleAvatar(
                backgroundImage: AssetImage(tOmSymbol),
                backgroundColor: Colors.transparent,
              ),
            ),
            accountName: Text('Spiritual Seeker',
                style:
                    TextStyle(color: tWhiteColor, fontWeight: FontWeight.bold)),
            accountEmail: Text('Om Namah Shivaya',
                style: TextStyle(color: tSpiritualCream)),
          ),
          _buildDrawerItem(Icons.home, 'Home', isDark),
          _buildDrawerItem(Icons.self_improvement, 'Meditation', isDark),
          _buildDrawerItem(Icons.book, 'Sacred Texts', isDark),
          _buildDrawerItem(Icons.calendar_today, 'Spiritual Calendar', isDark),
          _buildDrawerItem(Icons.music_note, 'Mantras', isDark),
          _buildDrawerItem(Icons.temple_hindu, 'Temples', isDark),
          _buildDrawerItem(Icons.settings, 'Settings', isDark),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(IconData icon, String title, bool isDark) {
    return ListTile(
      leading: Icon(icon, color: isDark ? tSpiritualOrange : tSpiritualMaroon),
      title: Text(title,
          style: TextStyle(
            color: isDark ? tWhiteColor : tSpiritualMaroon,
            fontWeight: FontWeight.w500,
          )),
      onTap: () {
        Get.back();
      },
    );
  }

  // Priority #1: Ram Naam Jaap Section (Main Feature)
  Widget _buildRamNaamJaapSection(
      TextTheme txtTheme, bool isDark, AppLocalizations localizations) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        gradient: LinearGradient(
          colors: isDark
              ? [tSpiritualMaroon, tSpiritualDeepOrange]
              : [tSpiritualSaffron, tSpiritualOrange],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: isDark
                ? tSpiritualMaroon.withOpacity(0.4)
                : tSpiritualOrange.withOpacity(0.4),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Ram Symbol/Icon
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: tWhiteColor.withOpacity(0.2),
              border: Border.all(color: tWhiteColor.withOpacity(0.3), width: 2),
            ),
            child: Image.asset(
              tRamSymbol,
              width: 50,
              height: 50,
              color: tWhiteColor,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.edit,
                  size: 50,
                  color: tWhiteColor,
                );
              },
            ),
          ),
          const SizedBox(height: 20),

          // Main Title
          Text(
            localizations.ramNaamJaapTitle,
            style: txtTheme.displayLarge?.copyWith(
              color: tWhiteColor,
              fontWeight: FontWeight.bold,
              fontSize: 28,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            localizations.ramNaamJaapSubtitle,
            style: txtTheme.headlineMedium?.copyWith(
              color: tWhiteColor.withOpacity(0.9),
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 16),

          // Description
          Text(
            localizations.writeRamName,
            style: txtTheme.bodyLarge?.copyWith(
              color: tWhiteColor.withOpacity(0.8),
              fontSize: 16,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Start Button
          Container(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => Get.to(() => Writer108BoxScreen()),
              style: ElevatedButton.styleFrom(
                backgroundColor: tWhiteColor,
                foregroundColor: isDark ? tSpiritualMaroon : tSpiritualOrange,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                elevation: 0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.edit, size: 24),
                  const SizedBox(width: 12),
                  Text(
                    localizations.startWritingRam,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Priority #2: Ram Devotion Section
  Widget _buildRamDevotionSection(
      TextTheme txtTheme, bool isDark, AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.ramDevotion,
          style: txtTheme.headlineMedium?.apply(
            color: isDark ? tWhiteColor : tSpiritualMaroon,
            fontWeightDelta: 2,
          ),
        ),
        const SizedBox(height: 15),
        Row(
          children: [
            Expanded(
              child: _buildRamDevotionCard(
                title: localizations.ramMantra,
                subtitle: localizations.shrirRamJayRam,
                icon: Icons.self_improvement,
                colors: isDark
                    ? [tSpiritualDeepOrange, tSpiritualOrange]
                    : [tSpiritualLotus, tSpiritualSaffron],
                onTap: () {
                  // Navigate to Ram mantras
                },
              ),
            ),
            const SizedBox(width: 15),
            Expanded(
              child: _buildRamDevotionCard(
                title: localizations.ramChalisa,
                subtitle: localizations.fortyVerses,
                icon: Icons.book,
                colors: isDark
                    ? [tSpiritualOrange, tSpiritualGold]
                    : [tSpiritualGold, tSpiritualYellow],
                onTap: () {
                  // Navigate to Ram Chalisa
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRamDevotionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required List<Color> colors,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 120,
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
          gradient: LinearGradient(
            colors: colors.map((color) => color.withOpacity(0.8)).toList(),
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: colors.first.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: tWhiteColor.withOpacity(0.2),
              ),
              child: Icon(
                icon,
                size: 28,
                color: tWhiteColor,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                color: tWhiteColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: tWhiteColor.withOpacity(0.8),
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  // Priority #3: Today's Ram Guidance
  Widget _buildTodaysRamGuidance(
      TextTheme txtTheme, bool isDark, AppLocalizations localizations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          localizations.todaysRamGuidance,
          style: txtTheme.headlineMedium?.apply(
            color: isDark ? tWhiteColor : tSpiritualMaroon,
            fontWeightDelta: 2,
          ),
        ),
        const SizedBox(height: 15),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(15),
            gradient: LinearGradient(
              colors: isDark
                  ? [
                      tSpiritualMaroon.withOpacity(0.6),
                      tSpiritualDeepOrange.withOpacity(0.6)
                    ]
                  : [
                      tSpiritualSaffron.withOpacity(0.7),
                      tSpiritualOrange.withOpacity(0.7)
                    ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            boxShadow: [
              BoxShadow(
                color: isDark
                    ? tSpiritualMaroon.withOpacity(0.2)
                    : tSpiritualOrange.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.format_quote,
                    color: tWhiteColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    localizations.ramTeaching,
                    style: const TextStyle(
                      color: tWhiteColor,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                localizations.raghukulRiti,
                style: const TextStyle(
                  color: tWhiteColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  height: 1.4,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                localizations.raghukulRitiTranslation,
                style: TextStyle(
                  color: tWhiteColor.withOpacity(0.9),
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Secondary: Other Practices (Reduced Priority)
  Widget _buildOtherPracticesGrid(
      TextTheme txtTheme, bool isDark, AppLocalizations localizations) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 3,
      crossAxisSpacing: 12,
      mainAxisSpacing: 12,
      childAspectRatio: 1.0,
      children: [
        _buildSmallPracticeCard(
          title: localizations.meditation,
          icon: Icons.self_improvement,
          colors: isDark
              ? [tSpiritualDeepOrange, tSpiritualOrange]
              : [tSpiritualLotus, tSpiritualSaffron],
          onTap: () {},
        ),
        _buildSmallPracticeCard(
          title: localizations.prayers,
          icon: Icons.favorite,
          colors: isDark
              ? [tSpiritualOrange, tSpiritualGold]
              : [tSpiritualGold, tSpiritualYellow],
          onTap: () {},
        ),
        _buildSmallPracticeCard(
          title: localizations.temple,
          icon: Icons.temple_hindu,
          colors: isDark
              ? [tSpiritualGold, tSpiritualYellow]
              : [tSpiritualBrown, tSpiritualMaroon],
          onTap: () {},
        ),
      ],
    );
  }

  Widget _buildSmallPracticeCard({
    required String title,
    required IconData icon,
    required List<Color> colors,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: colors.map((color) => color.withOpacity(0.7)).toList(),
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: colors.first.withOpacity(0.2),
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: tWhiteColor.withOpacity(0.2),
              ),
              child: Icon(
                icon,
                size: 22,
                color: tWhiteColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color: tWhiteColor,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
