import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:login_flutter_app/src/constants/colors.dart';
import 'package:login_flutter_app/src/constants/image_strings.dart';
import 'package:login_flutter_app/src/constants/sizes.dart';
import 'package:login_flutter_app/src/constants/text_strings.dart';
import 'package:login_flutter_app/src/features/core/screens/dashboard/widgets/spiritual_appbar.dart';
import 'package:login_flutter_app/src/features/core/screens/dashboard/widgets/spiritual_greeting.dart';
import 'package:login_flutter_app/src/features/core/screens/dashboard/widgets/spiritual_practices_grid.dart';
import 'package:login_flutter_app/src/features/core/screens/dashboard/widgets/spiritual_daily_cards.dart';
import 'package:login_flutter_app/src/features/core/screens/dashboard/widgets/spiritual_deities_section.dart';
import 'package:login_flutter_app/src/features/core/screens/dashboard/widgets/spiritual_quick_actions.dart';

class SpiritualDashboard extends StatelessWidget {
  const SpiritualDashboard({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    //Variables
    final txtTheme = Theme.of(context).textTheme;
    final isDark = MediaQuery.of(context).platformBrightness == Brightness.dark;

    return SafeArea(
      child: Scaffold(
        backgroundColor: isDark ? tDarkColor : tSpiritualCream,
        appBar: SpiritualAppBar(isDark: isDark),
        drawer: _buildSpiritualDrawer(isDark),
        body: SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.all(tDashboardPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Spiritual Greeting Section
                SpiritualGreeting(txtTheme: txtTheme, isDark: isDark),
                const SizedBox(height: tDashboardPadding),

                // Daily Spiritual Cards
                SpiritualDailyCards(txtTheme: txtTheme, isDark: isDark),
                const SizedBox(height: tDashboardPadding),

                // Spiritual Practices Grid
                Text(tSpiritualPractices, 
                    style: txtTheme.headlineMedium?.apply(
                      color: isDark ? tWhiteColor : tSpiritualMaroon,
                      fontWeightDelta: 2
                    )),
                const SizedBox(height: 10),
                SpiritualPracticesGrid(txtTheme: txtTheme, isDark: isDark),
                const SizedBox(height: tDashboardPadding),

                // Deities Section
                Text(tDeities, 
                    style: txtTheme.headlineMedium?.apply(
                      color: isDark ? tWhiteColor : tSpiritualMaroon,
                      fontWeightDelta: 2
                    )),
                const SizedBox(height: 10),
                SpiritualDeitiesSection(txtTheme: txtTheme, isDark: isDark),
                const SizedBox(height: tDashboardPadding),

                // Quick Actions
                Text(tQuickActions, 
                    style: txtTheme.headlineMedium?.apply(
                      color: isDark ? tWhiteColor : tSpiritualMaroon,
                      fontWeightDelta: 2
                    )),
                const SizedBox(height: 10),
                SpiritualQuickActions(txtTheme: txtTheme, isDark: isDark),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSpiritualDrawer(bool isDark) {
    return Drawer(
      backgroundColor: isDark ? tSecondaryColor : tSpiritualCream,
      child: ListView(
        children: [
          UserAccountsDrawerHeader(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: isDark 
                  ? [tSpiritualMaroon, tSpiritualDeepOrange]
                  : [tSpiritualSaffron, tSpiritualOrange],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
            ),
            currentAccountPicture: Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: tWhiteColor, width: 2),
              ),
              child: const CircleAvatar(
                backgroundImage: AssetImage(tOmSymbol),
                backgroundColor: Colors.transparent,
              ),
            ),
            accountName: Text('Spiritual Seeker', 
                style: TextStyle(color: tWhiteColor, fontWeight: FontWeight.bold)),
            accountEmail: Text('Om Namah Shivaya', 
                style: TextStyle(color: tSpiritualCream)),
          ),
          _buildDrawerItem(Icons.home, 'Home', isDark),
          _buildDrawerItem(Icons.self_improvement, 'Meditation', isDark),
          _buildDrawerItem(Icons.book, 'Sacred Texts', isDark),
          _buildDrawerItem(Icons.calendar_today, 'Spiritual Calendar', isDark),
          _buildDrawerItem(Icons.music_note, 'Mantras', isDark),
          _buildDrawerItem(Icons.temple_hindu, 'Temples', isDark),
          _buildDrawerItem(Icons.settings, 'Settings', isDark),
        ],
      ),
    );
  }

  Widget _buildDrawerItem(IconData icon, String title, bool isDark) {
    return ListTile(
      leading: Icon(icon, color: isDark ? tSpiritualOrange : tSpiritualMaroon),
      title: Text(title, style: TextStyle(
        color: isDark ? tWhiteColor : tSpiritualMaroon,
        fontWeight: FontWeight.w500,
      )),
      onTap: () {
        Get.back();
      },
    );
  }
}
