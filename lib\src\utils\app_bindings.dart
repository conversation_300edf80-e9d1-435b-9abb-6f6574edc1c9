import 'package:get/get.dart';
import 'package:login_flutter_app/src/features/authentication/controllers/login_controller.dart';
import 'package:login_flutter_app/src/features/authentication/controllers/on_boarding_controller.dart';
import 'package:login_flutter_app/src/features/authentication/controllers/otp_controller.dart';
import 'package:login_flutter_app/src/features/authentication/controllers/signup_controller.dart';
import 'package:login_flutter_app/src/repository/user_repository/user_repository.dart';
import 'package:login_flutter_app/src/features/core/controllers/user_preferences_controller.dart';
import 'package:login_flutter_app/src/services/firebase_service.dart';
import '../repository/authentication_repository/authentication_repository.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => AuthenticationRepository(), fenix: true);
    Get.lazyPut(() => UserRepository(), fenix: true);
    Get.lazyPut(() => UserPreferencesController(), fenix: true);

    // Firebase service should already be initialized in main.dart
    // Get.lazyPut(() => FirebaseService(), fenix: true);

    Get.lazyPut(() => OnBoardingController(), fenix: true);

    Get.lazyPut(() => LoginController(), fenix: true);
    Get.lazyPut(() => SignUpController(), fenix: true);
    Get.lazyPut(() => OTPController(), fenix: true);
  }
}
