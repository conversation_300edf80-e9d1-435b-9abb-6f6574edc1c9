import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:line_awesome_flutter/line_awesome_flutter.dart';
import 'package:login_flutter_app/src/constants/colors.dart';
import 'package:login_flutter_app/src/constants/image_strings.dart';
import 'package:login_flutter_app/src/features/core/controllers/theme_controller.dart';
import 'package:login_flutter_app/src/services/firebase_service.dart';
import 'package:login_flutter_app/src/localization/app_localizations.dart';
import '../controller/writer_controller.dart';

class Writer108BoxScreen extends StatefulWidget {
  const Writer108BoxScreen({Key? key}) : super(key: key);

  @override
  State<Writer108BoxScreen> createState() => _Writer108BoxScreenState();
}

class _Writer108BoxScreenState extends State<Writer108BoxScreen> {
  final ScrollController _gridScrollController = ScrollController();
  late WriterController writerController;
  late ThemeController themeController;

  @override
  void initState() {
    super.initState();
    writerController = Get.put(WriterController());
    themeController = Get.find<ThemeController>();

    // Listen to write count changes for auto-scroll
    ever(writerController.writeCount, (int count) {
      if (count > 0) {
        _autoScrollToCurrentPosition(count);
      }
    });

    // Listen to page changes to reset scroll position
    ever(writerController.currentPage, (int page) {
      _resetScrollToTop();
    });
  }

  @override
  void dispose() {
    _gridScrollController.dispose();
    super.dispose();
  }

  // Auto-scroll to keep current writing position visible
  void _autoScrollToCurrentPosition(int currentCount) {
    if (!_gridScrollController.hasClients) return;

    // Calculate current row (0-based)
    final currentRow = (currentCount - 1) ~/ 9;
    const totalRows = 12;

    // Only scroll if we're past the first few rows
    if (currentRow >= 3) {
      // Calculate the scroll position to center the current row
      final maxScrollExtent = _gridScrollController.position.maxScrollExtent;
      final targetScrollPosition =
          (currentRow - 2) * (maxScrollExtent / (totalRows - 6));

      // Smooth scroll to position
      _gridScrollController.animateTo(
        targetScrollPosition.clamp(0.0, maxScrollExtent),
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  // Reset scroll position to top when starting new page
  void _resetScrollToTop() {
    if (!_gridScrollController.hasClients) return;

    // Smooth scroll back to top
    _gridScrollController.animateTo(
      0.0,
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    // Log screen view to Firebase Analytics
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (Get.isRegistered<FirebaseService>()) {
        FirebaseService.instance.logScreenView(
          screenName: 'ram_naam_jaap_writer',
          screenClass: 'Writer108BoxScreen',
          parameters: {
            'total_boxes': 108,
            'grid_size': '9x12',
          },
        );
      }
    });

    return Scaffold(
      backgroundColor: isDark ? tSecondaryColor : tSpiritualCream,
      appBar: _buildSpiritualAppBar(context, localizations, isDark),
      body: SafeArea(
        child: Stack(
          children: [
            // Main content
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  // Compact header with essential info only
                  _buildCompactHeader(context, localizations, isDark),
                  const SizedBox(height: 8),

                  // Progress section - always visible
                  _buildCompactProgress(context, localizations, isDark),
                  const SizedBox(height: 8),

                  // RAM Grid - main focus area
                  Expanded(
                    child: _buildRamGrid(context, isDark),
                  ),
                  const SizedBox(height: 8),

                  // Write button - always visible and prominent
                  _buildWriteButton(context, localizations, isDark),
                  const SizedBox(height: 8),

                  // Color customization - can be scrolled if needed (one-time setting)
                  _buildCompactColorCustomization(
                      context, localizations, isDark),
                ],
              ),
            ),

            // Page completion overlay
            Obx(() => writerController.isPageCompleted.value
                ? _buildPageCompletionOverlay(context, localizations, isDark)
                : const SizedBox.shrink()),
          ],
        ),
      ),
    );
  }

  /// Build spiritual app bar with Om symbol and theme toggle
  PreferredSizeWidget _buildSpiritualAppBar(
      BuildContext context, AppLocalizations localizations, bool isDark) {
    return AppBar(
      backgroundColor: isDark ? tSpiritualMaroon : tSpiritualSaffron,
      foregroundColor: tWhiteColor,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(Icons.arrow_back_ios, color: tWhiteColor),
      ),
      title: Row(
        children: [
          Image.asset(
            tOmSymbol,
            width: 24,
            height: 24,
            color: tWhiteColor,
            errorBuilder: (context, error, stackTrace) {
              return const Icon(Icons.self_improvement,
                  color: tWhiteColor, size: 24);
            },
          ),
          const SizedBox(width: 8),
          Text(
            localizations.jaap,
            style: const TextStyle(
              color: tWhiteColor,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
        ],
      ),
      actions: [
        Obx(() => IconButton(
              onPressed: () => themeController.toggleTheme(),
              icon: Icon(
                themeController.isDarkMode.value
                    ? Icons.light_mode
                    : Icons.dark_mode,
                color: tWhiteColor,
              ),
            )),
      ],
    );
  }

  /// Build compact header for no-scroll layout
  Widget _buildCompactHeader(
      BuildContext context, AppLocalizations localizations, bool isDark) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isDark
              ? [
                  tSpiritualMaroon.withOpacity(0.2),
                  tSpiritualDeepOrange.withOpacity(0.2)
                ]
              : [
                  tSpiritualSaffron.withOpacity(0.1),
                  tSpiritualOrange.withOpacity(0.1)
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Obx(() => Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Daily progress
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'Today: ${writerController.totalPagesCompletedToday.value} pages',
                    style: TextStyle(
                      fontSize: 10,
                      color: isDark
                          ? tWhiteColor.withOpacity(0.7)
                          : tSpiritualMaroon.withOpacity(0.7),
                    ),
                  ),
                  if (writerController.totalPagesCompletedToday.value > 0)
                    Text(
                      '${writerController.totalPagesCompletedToday.value * 108} RAMs written',
                      style: TextStyle(
                        fontSize: 8,
                        color: isDark
                            ? tSpiritualOrange.withOpacity(0.8)
                            : tSpiritualSaffron.withOpacity(0.8),
                      ),
                    ),
                ],
              ),

              // Main title
              Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    localizations.ramNaamJaapTitle,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isDark ? tSpiritualOrange : tSpiritualMaroon,
                    ),
                  ),
                  Text(
                    'Page ${writerController.currentPage.value}',
                    style: TextStyle(
                      fontSize: 12,
                      color: isDark
                          ? tWhiteColor.withOpacity(0.8)
                          : tSpiritualMaroon.withOpacity(0.8),
                    ),
                  ),
                ],
              ),

              // Page completion status
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (writerController.isPageCompleted.value)
                    const Icon(
                      Icons.check_circle,
                      color: Colors.green,
                      size: 16,
                    ),
                  Text(
                    '(108)',
                    style: TextStyle(
                      fontSize: 12,
                      color: isDark
                          ? tWhiteColor.withOpacity(0.7)
                          : tSpiritualMaroon.withOpacity(0.7),
                    ),
                  ),
                ],
              ),
            ],
          )),
    );
  }

  /// Build the 9x12 grid for writing RAM - optimized for no-scroll layout
  Widget _buildRamGrid(BuildContext context, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isDark ? tSecondaryColor.withOpacity(0.5) : tWhiteColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isDark
              ? tSpiritualOrange.withOpacity(0.3)
              : tSpiritualSaffron.withOpacity(0.3),
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: (isDark ? tSpiritualOrange : tSpiritualSaffron)
                .withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Obx(
        () => GridView.builder(
          controller: _gridScrollController, // Add scroll controller
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 9,
            crossAxisSpacing: 1,
            mainAxisSpacing: 1,
            childAspectRatio: 1,
          ),
          itemCount: writerController.stringList.length,
          itemBuilder: (context, index) {
            final isWritten = writerController.stringList[index].isNotEmpty;
            final isNextToWrite = index == writerController.writeCount.value;

            return AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: BoxDecoration(
                color:
                    _getCellBackgroundColor(isWritten, isNextToWrite, isDark),
                borderRadius: BorderRadius.circular(2),
                border: Border.all(
                  color: _getCellBorderColor(isWritten, isNextToWrite, isDark),
                  width: isNextToWrite ? 1.5 : 0.5,
                ),
                boxShadow: isNextToWrite
                    ? [
                        BoxShadow(
                          color: (isDark ? tSpiritualOrange : tSpiritualSaffron)
                              .withOpacity(0.4),
                          blurRadius: 4,
                          offset: const Offset(0, 0),
                        ),
                      ]
                    : null,
              ),
              child: Center(
                child: isNextToWrite && !isWritten
                    ? Icon(
                        Icons.edit,
                        size: 8,
                        color: isDark ? tSpiritualOrange : tSpiritualSaffron,
                      )
                    : Text(
                        writerController.stringList[index],
                        style: TextStyle(
                          fontSize: 10,
                          color: writerController.selectedTextColor.value,
                          fontWeight:
                              isWritten ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
              ),
            );
          },
        ),
      ),
    );
  }

  // Helper method to get cell background color with visual indicators
  Color _getCellBackgroundColor(
      bool isWritten, bool isNextToWrite, bool isDark) {
    if (isNextToWrite && !isWritten) {
      return isDark
          ? tSpiritualOrange.withOpacity(0.1)
          : tSpiritualSaffron.withOpacity(0.1);
    }
    return writerController.selectedCellColor.value;
  }

  // Helper method to get cell border color with visual indicators
  Color _getCellBorderColor(bool isWritten, bool isNextToWrite, bool isDark) {
    if (isNextToWrite && !isWritten) {
      return isDark ? tSpiritualOrange : tSpiritualSaffron;
    }
    if (isWritten) {
      return (isDark ? tSpiritualOrange : tSpiritualSaffron).withOpacity(0.5);
    }
    return (isDark ? tWhiteColor : tSpiritualMaroon).withOpacity(0.2);
  }

  /// Build compact progress section for side panel
  Widget _buildCompactProgress(
      BuildContext context, AppLocalizations localizations, bool isDark) {
    return Obx(() => Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark
                  ? [
                      tSpiritualMaroon.withOpacity(0.2),
                      tSpiritualDeepOrange.withOpacity(0.2)
                    ]
                  : [
                      tSpiritualSaffron.withOpacity(0.1),
                      tSpiritualOrange.withOpacity(0.1)
                    ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              // Count display
              Text(
                "${writerController.writeCount.value}/108",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isDark ? tSpiritualOrange : tSpiritualMaroon,
                ),
              ),
              const SizedBox(width: 16),

              // Progress bar
              Expanded(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(6),
                  child: LinearProgressIndicator(
                    value: writerController.progressValue.value,
                    backgroundColor: isDark ? tSecondaryColor : tSpiritualCream,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isDark ? tSpiritualOrange : tSpiritualSaffron,
                    ),
                    minHeight: 8,
                  ),
                ),
              ),
              const SizedBox(width: 16),

              // Percentage
              Text(
                "${(writerController.progressValue.value * 100).toInt()}%",
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: isDark ? tSpiritualOrange : tSpiritualMaroon,
                ),
              ),
            ],
          ),
        ));
  }

  /// Build the main write button - horizontal layout for vertical design
  Widget _buildWriteButton(
      BuildContext context, AppLocalizations localizations, bool isDark) {
    return Obx(() => Container(
          width: double.infinity,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: isDark
                  ? [tSpiritualMaroon, tSpiritualDeepOrange]
                  : [tSpiritualSaffron, tSpiritualOrange],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(30),
            boxShadow: [
              BoxShadow(
                color: (isDark ? tSpiritualOrange : tSpiritualSaffron)
                    .withOpacity(0.3),
                blurRadius: 12,
                offset: const Offset(0, 6),
              ),
            ],
          ),
          child: ElevatedButton(
            onPressed: writerController.writeCount.value < 108
                ? () {
                    writerController.addToStringList();
                    // Log Firebase Analytics
                    if (Get.isRegistered<FirebaseService>()) {
                      FirebaseService.instance.logEvent(
                        name: 'ram_naam_written',
                        parameters: {
                          'count': writerController.writeCount.value,
                          'progress_percentage':
                              (writerController.progressValue.value * 100)
                                  .round(),
                        },
                      );
                    }
                  }
                : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.transparent,
              shadowColor: Colors.transparent,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(30),
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.edit,
                  color: tWhiteColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  localizations.writeRam,
                  style: const TextStyle(
                    color: tWhiteColor,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  /// Build compact color customization for side panel
  Widget _buildCompactColorCustomization(
      BuildContext context, AppLocalizations localizations, bool isDark) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isDark ? tSecondaryColor.withOpacity(0.3) : tWhiteColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isDark
              ? tSpiritualOrange.withOpacity(0.3)
              : tSpiritualSaffron.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            localizations.customizeColors,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: isDark ? tSpiritualOrange : tSpiritualMaroon,
            ),
          ),
          const SizedBox(height: 8),

          // Text Colors - Compact grid
          Text(
            localizations.textColor,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: isDark
                  ? tWhiteColor.withOpacity(0.8)
                  : tSpiritualMaroon.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 4),
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children: [
              _buildCompactColorButton(tSpiritualMaroon,
                  () => writerController.changeTextColor(tSpiritualMaroon)),
              _buildCompactColorButton(tSpiritualSaffron,
                  () => writerController.changeTextColor(tSpiritualSaffron)),
              _buildCompactColorButton(tSpiritualDeepOrange,
                  () => writerController.changeTextColor(tSpiritualDeepOrange)),
              _buildCompactColorButton(Colors.black,
                  () => writerController.changeTextColor(Colors.black)),
            ],
          ),

          const SizedBox(height: 8),

          // Background Colors - Compact grid
          Text(
            localizations.backgroundColor,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: isDark
                  ? tWhiteColor.withOpacity(0.8)
                  : tSpiritualMaroon.withOpacity(0.8),
            ),
          ),
          const SizedBox(height: 4),
          Wrap(
            spacing: 4,
            runSpacing: 4,
            children: [
              _buildCompactColorButton(tSpiritualCream,
                  () => writerController.changeCellColor(tSpiritualCream)),
              _buildCompactColorButton(
                  tSpiritualLightOrange,
                  () =>
                      writerController.changeCellColor(tSpiritualLightOrange)),
              _buildCompactColorButton(tSpiritualLotus,
                  () => writerController.changeCellColor(tSpiritualLotus)),
              _buildCompactColorButton(Colors.white,
                  () => writerController.changeCellColor(Colors.white)),
            ],
          ),
        ],
      ),
    );
  }

  /// Build compact color button for side panel
  Widget _buildCompactColorButton(Color color, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 24,
        height: 24,
        decoration: BoxDecoration(
          color: color,
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.grey.withOpacity(0.5),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withOpacity(0.3),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
      ),
    );
  }

  /// Build page completion overlay
  Widget _buildPageCompletionOverlay(
      BuildContext context, AppLocalizations localizations, bool isDark) {
    return Container(
      color: Colors.black.withOpacity(0.7),
      child: Center(
        child: Container(
          margin: const EdgeInsets.all(32),
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: isDark ? tSecondaryColor : tWhiteColor,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isDark ? tSpiritualOrange : tSpiritualSaffron,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: (isDark ? tSpiritualOrange : tSpiritualSaffron)
                    .withOpacity(0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Completion icon
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check_circle,
                  color: Colors.green,
                  size: 48,
                ),
              ),
              const SizedBox(height: 16),

              // Congratulations text
              Text(
                'Page ${writerController.currentPage.value} Completed!',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: isDark ? tSpiritualOrange : tSpiritualMaroon,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),

              Text(
                '108 RAMs written successfully',
                style: TextStyle(
                  fontSize: 14,
                  color: isDark
                      ? tWhiteColor.withOpacity(0.8)
                      : tSpiritualMaroon.withOpacity(0.8),
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),

              // Daily progress
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isDark
                      ? tSpiritualOrange.withOpacity(0.1)
                      : tSpiritualSaffron.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Text(
                      'Today\'s Progress',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: isDark ? tSpiritualOrange : tSpiritualMaroon,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${writerController.totalPagesCompletedToday.value} pages completed',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: isDark ? tWhiteColor : tSpiritualMaroon,
                      ),
                    ),
                    Text(
                      '${writerController.totalPagesCompletedToday.value * 108} total RAMs',
                      style: TextStyle(
                        fontSize: 12,
                        color: isDark
                            ? tWhiteColor.withOpacity(0.8)
                            : tSpiritualMaroon.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              Text(
                'Moving to next page in 2 seconds...',
                style: TextStyle(
                  fontSize: 12,
                  color: isDark
                      ? tWhiteColor.withOpacity(0.6)
                      : tSpiritualMaroon.withOpacity(0.6),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
