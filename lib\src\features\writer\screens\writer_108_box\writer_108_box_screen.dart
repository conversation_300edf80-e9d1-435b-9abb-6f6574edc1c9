import 'package:flutter/material.dart';
import 'package:flutter_rounded_progress_bar/flutter_rounded_progress_bar.dart';
import 'package:flutter_rounded_progress_bar/rounded_progress_bar_style.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:get/get.dart';
import 'package:line_awesome_flutter/line_awesome_flutter.dart';
import 'package:login_flutter_app/src/common_widgets/buttons/primary_button.dart';
import 'package:login_flutter_app/src/constants/sizes.dart';
import 'package:login_flutter_app/src/constants/text_strings.dart';
import 'package:login_flutter_app/src/features/core/screens/profile/update_profile_screen.dart';
import 'package:login_flutter_app/src/features/core/screens/profile/widgets/image_with_icon.dart';
import 'package:login_flutter_app/src/features/core/screens/profile/widgets/profile_menu.dart';
import 'package:login_flutter_app/src/features/core/controllers/theme_controller.dart';
import 'package:login_flutter_app/src/localization/app_localizations.dart';

import '../../../../common_widgets/common.dart';
import '../../../../repository/authentication_repository/authentication_repository.dart';
import '../../../core/screens/profile/all_users.dart';
import '../controller/writer_controller.dart';

class Writer108BoxScreen extends StatelessWidget {
  Writer108BoxScreen({Key? key}) : super(key: key);
  final WriterController writerController = Get.put(WriterController());
  final ThemeController themeController = Get.find<ThemeController>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
            onPressed: () => Get.back(),
            icon: const Icon(LineAwesomeIcons.angle_left)),
        title:
            Text(tProfile, style: Theme.of(context).textTheme.headlineMedium),
        actions: [
          Obx(() => IconButton(
              onPressed: () => themeController.toggleTheme(),
              icon: Icon(themeController.isDarkMode.value
                  ? LineAwesomeIcons.sun
                  : LineAwesomeIcons.moon)))
        ],
      ),
      body: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.all(tDefaultSpace),
          child: Column(
            children: [
              // Text(tProfileHeading, style: Theme.of(context).textTheme.headlineMedium),
              Text(tProfileSubHeading,
                  style: Theme.of(context).textTheme.bodyMedium),
              // const SizedBox(height: 20),
              const Divider(),
              Container(
                height: 350,
                child: Obx(
                  () => AlignedGridView.count(
                    itemCount: writerController.stringList.length,
                    crossAxisCount: 9,
                    mainAxisSpacing: 2,
                    crossAxisSpacing: 2,
                    itemBuilder: (context, index) {
                      return Container(
                        color: writerController.selectedCellColor.value,
                        child: Center(
                          child: Text(
                            writerController.stringList[index],
                            style: TextStyle(
                              fontSize: 14,
                              color: writerController.selectedTextColor.value,
                              fontWeight:
                                  writerController.stringList[index].isNotEmpty
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                            // extent: (index % 7 + 1) * 30,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
              // const SizedBox(height: 10),
              // Obx for observing changes in progressValue and writeCount
              Obx(() => Column(
                    children: [
                      LinearProgressIndicator(
                        value: writerController.progressValue.value,
                      ),
                      const SizedBox(height: 10),
                      Text("Jaap Count: ${writerController.writeCount.value}"),
                      // SimpleAnimationProgressBar(
                      //   height: 30,
                      //   width: 300,
                      //
                      //   backgroundColor: Colors.grey.shade800,
                      //   foregrondColor: Colors.purple,
                      //   ratio: writerController.progressValue.value,
                      //   direction: Axis.horizontal,
                      //   curve: Curves.fastLinearToSlowEaseIn,
                      //   duration: const Duration(seconds: 1),
                      //   borderRadius: BorderRadius.circular(10),
                      //   gradientColor: const LinearGradient(
                      //       colors: [Colors.pink, Colors.purple]),
                      //   boxShadow: const [
                      //     BoxShadow(
                      //       color: Colors.pink,
                      //       offset: Offset(
                      //         5.0,
                      //         5.0,
                      //       ),
                      //       blurRadius: 10.0,
                      //       spreadRadius: 2.0,
                      //     ),
                      //   ],
                      // )
                      // RoundedProgressBar(
                      //   height: 30,
                      //   milliseconds: 100,
                      //     childLeft: Text("${writerController.writeCount.value}%",
                      //         style: TextStyle(color: Colors.white)),
                      //     percent:  writerController.progressValue.value,
                      //     theme: RoundedProgressBarTheme.green),
                    ],
                  )),

              const SizedBox(height: 30),
              TPrimaryButton(
                  isFullWidth: false,
                  width: 70,
                  text: tWriteName,
                  onPressed: () {
                    writerController.addToStringList();
                  }),
              const SizedBox(height: 10),

              /// -- BUTTON change text color
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      writerController.changeTextColor(Colors.red);
                    },
                    child: Text(""),
                    style: buildButtonStyle(Colors.red),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      writerController.changeTextColor(Colors.green);
                    },
                    child: Text(""),
                    style: buildButtonStyle(Colors.green),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      writerController.changeTextColor(Colors.blue);
                    },
                    child: Text(""),
                    style: buildButtonStyle(Colors.blue),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      writerController.changeTextColor(Colors.grey);
                    },
                    child: Text(""),
                    style: buildButtonStyle(Colors.grey),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      writerController.changeTextColor(Colors.blueGrey);
                    },
                    style: buildButtonStyle(Colors.blueGrey),
                    child: const Text(""),
                  ),
                ],
              ),
              const SizedBox(height: 10),

              /// -- BUTTON change cell color
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ElevatedButton(
                    onPressed: () {
                      writerController.changeCellColor(Colors.red[100]!);
                    },
                    child: Text(""),
                    style: buildButtonStyle(Colors.red[100]!),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      writerController.changeCellColor(Colors.green[100]!);
                    },
                    child: Text(""),
                    style: buildButtonStyle(Colors.green[100]!),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      writerController.changeCellColor(Colors.blue[100]!);
                    },
                    child: Text(""),
                    style: buildButtonStyle(Colors.blue[100]!),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      writerController.changeCellColor(Colors.grey[100]!);
                    },
                    child: Text(""),
                    style: buildButtonStyle(Colors.grey[100]!),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      writerController.changeCellColor(Colors.blueGrey[100]!);
                    },
                    child: Text(""),
                    style: buildButtonStyle(Colors.blueGrey[100]!),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  ButtonStyle buildButtonStyle(color) {
    return ButtonStyle(
        shape: MaterialStateProperty.all(CircleBorder()),
        padding: MaterialStateProperty.all(EdgeInsets.all(8.0)),
        backgroundColor: MaterialStateProperty.all(color));
  }

  _showLogoutModal() {
    Get.defaultDialog(
      title: "LOGOUT",
      titleStyle: const TextStyle(fontSize: 20),
      content: const Padding(
        padding: EdgeInsets.symmetric(vertical: 15.0),
        child: Text("Are you sure, you want to Logout?"),
      ),
      confirm: TPrimaryButton(
        isFullWidth: false,
        onPressed: () => AuthenticationRepository.instance.logout(),
        text: "Yes",
      ),
      cancel: SizedBox(
          width: 100,
          child: OutlinedButton(
              onPressed: () => Get.back(), child: const Text("No"))),
    );
  }
}
