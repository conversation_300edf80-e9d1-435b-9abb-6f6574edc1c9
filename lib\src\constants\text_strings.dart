/* -- App Text Strings -- */

// -- GLOBAL Texts
const String tNo = "No";
const String tYes = "Yes";
const String tNext = "Next";
const String tLogin = "Login";
const String tEmail = "E-Mail";
const String tSignup = "Signup";
const String tLogout = "Logout";
const String tSuccess = "Success";
const String tPhoneNo = "Phone No";
const String tContinue = "Continue";
const String tPassword = "Password";
const String tFullName = "Full Name";
const String tGetStarted = "Get Started";
const String tForgetPassword = "Forget Password?";
const String tSignInWithGoogle = "Sign-In with Google";

// -- Validation --
const String tEmailCannotEmpty = "Email cannot be empty";
const String tInvalidEmailFormat = "Invalid email format";
const String tNoRecordFound = "No record found";

// -- SnackBar --
const String tAlert = "Alert";
const String tOhSnap = "Oh Snap";
const String tEmailSent = "Hurray!!! Email is on its way.";
const String tCongratulations = "Congratulations";
const String tEmailLinkToResetPassword = "Email Link To Reset Password";
const String tAccountCreateVerifyEmail = "Account Create Verify Email";

// -- Splash Screen Text
const String tAppName = "/appable:";
const String tAppTagLine = "Learn To Code. \nFree For Everyone";

// -- On Boarding Text
const String tOnBoardingTitle1 = "Build Awesome Apps";
const String tOnBoardingTitle2 = "Learn from YouTube";
const String tOnBoardingTitle3 = "Get Code & Resources";
const String tOnBoardingSubTitle1 =
    "Let's start your journey with us on this amazing and easy platform.";
const String tOnBoardingSubTitle2 =
    "Get Video Tutorials of each topic to learn things easily.";
const String tOnBoardingSubTitle3 =
    "Save time by just copy pasting complete apps you learned from videos.";
const String tOnBoardingCounter1 = "1/3";
const String tOnBoardingCounter2 = "2/3";
const String tOnBoardingCounter3 = "3/3";

// -- Welcome Screen Text
const String tWelcomeTitle = "Build Awesome Apps";
const String tWelcomeSubTitle =
    "Let's put your creativity on the development highway.";

// -- Login Screen Text
const String tLoginTitle = "Welcome Back,";
const String tLoginSubTitle = "Make it work, make it right, make it fast.";
const String tRememberMe = "Remember Me?";
const String tDontHaveAnAccount = "Don't have an Account";
const String tEnterYour = "Enter your";
const String tResetPassword = "Reset Password";
const String tOR = "OR";
const String tConnectWith = "Connect With";
const String tFacebook = "Facebook";
const String tGoogle = "Google";

// -- Sign Up Screen Text
const String tSignUpTitle = "Get On Board!";
const String tSignUpSubTitle = "Create your profile to start your Journey.";
const String tAlreadyHaveAnAccount = "Already have an Account";

// -- Forget Password Text
const String tForgetPasswordTitle = "Make Selection!";
const String tForgetPasswordSubTitle =
    "Select one of the options given below to reset your password.";
const String tResetViaEMail = "Reset via Mail Verification";
const String tResetViaPhone = "Reset via Phone Verification";

// -- Forget Password Via Phone - Text
const String tForgetPhoneSubTitle =
    "Enter your registered Phone No to receive OTP";

// -- Forget Password Via E-Mail - Text
const String tForgetMailSubTitle =
    "Enter your registered E-Mail to receive OTP";

// -- OTP Screen - Text
const String tOtpTitle = "CO\nDE";
const String tOtpSubTitle = "Verification";
const String tOtpMessage = "Enter the verification code sent at ";

// -- Email Verification
const String tEmailVerificationTitle = "Verify your email address";
const String tEmailVerificationSubTitle =
    "We have just send email verification link on your email. Please check email and click on that link to verify your Email address. \n\n If not auto redirected after verification, click on the Continue button.";
const String tResendEmailLink = "Resend E-Mail Link";
const String tBackToLogin = "Back to login";

// -- Dashboard Screen - Text
const String tDashboardTitle = "Hey, Coding with T";
const String tDashboardHeading = "Explore Courses";
const String tDashboardSearch = "Search...";
const String tDashboardBannerTitle2 = "JAVA";
// const String tDashboardButton = "View All";
const String tDashboardButton = "Writer 108";
const String tDashboardTopCourses = "Top Courses";
const String tDashboardBannerSubTitle = "10 Lessons";
const String tDashboardBannerTitle1 = "Android for Beginners";

// -- Spiritual Dashboard - Text
const String tSpiritualGreeting = "Om Namah Shivaya";
const String tSpiritualSubGreeting = "Begin your spiritual journey today";
const String tTodaysMantra = "Today's Mantra";
const String tSpiritualPractices = "Spiritual Practices";
const String tRamNaamJaap = "Ram Naam Jaap";
const String tMeditation = "Meditation";
const String tPrayers = "Prayers";
const String tTempleVisit = "Temple";
const String tSacredTexts = "Sacred Texts";
const String tSpiritualCalendar = "Spiritual Calendar";
const String tQuickActions = "Quick Actions";
const String tStartJaap = "Start Jaap";
const String tPlayMantras = "Play Mantras";
const String tDailyPrayer = "Daily Prayer";
const String tSpiritualQuote = "Spiritual Quote";
const String tBlessings = "Divine Blessings";
const String tAuspiciousTime = "Auspicious Time";
const String tFestivals = "Festivals";
const String tDeities = "Deities";
const String tGanesh = "Ganesh";
const String tShiva = "Shiva";
const String tKrishna = "Krishna";
const String tHanuman = "Hanuman";
const String tDurga = "Durga";
const String tRam = "Ram";

// -- Profile Screen - Text
const String tProfile = "Profile";
const String tEditProfile = "Edit Profile";
const String tWriteName = "Write";
const String tLogoutDialogHeading = "Logout";
const String tProfileHeading = "Coding with T";
const String tProfileSubHeading = "Sub Heading";
// -- Menu
const String tMenu5 = tLogout;
const String tMenu1 = "Settings";
const String tMenu4 = "Information";
const String tMenu2 = "Billing Details";
const String tMenu3 = "User Management";

// -- Update Profile Screen - Text
const String tDelete = "Delete";
const String tJoined = "Joined";
const String tJoinedAt = " 31 October 2022";
