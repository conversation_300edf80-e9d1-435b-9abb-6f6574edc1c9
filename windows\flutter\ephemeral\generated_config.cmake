# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\fvm\\versions\\3.13.9" FLUTTER_ROOT)
file(TO_CMAKE_PATH "I:\\Dev\\ramnaamjaap" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.13.9"
  "PROJECT_DIR=I:\\Dev\\ramnaamjaap"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\fvm\\versions\\3.13.9"
  "FLUTTER_EPHEMERAL_DIR=I:\\Dev\\ramnaamjaap\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=I:\\Dev\\ramnaamjaap"
  "FLUTTER_TARGET=I:\\Dev\\ramnaamjaap\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC8wNTQ1Zjg3MDVkZjMwMTg3N2Q3ODcxMDdiYWMxYTZlOWZjOWVlMWFkLw=="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=I:\\Dev\\ramnaamjaap\\.dart_tool\\package_config.json"
)
