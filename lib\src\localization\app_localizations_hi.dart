import 'app_localizations.dart';

class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get appName => 'राम नाम जप';

  // Welcome & Authentication
  @override
  String get welcome => 'स्वागत';
  @override
  String get getStarted => 'शुरू करें';
  @override
  String get login => 'लॉगिन';
  @override
  String get signup => 'साइन अप';
  @override
  String get email => 'ईमेल';
  @override
  String get password => 'पासवर्ड';
  @override
  String get confirmPassword => 'पासवर्ड की पुष्टि करें';
  @override
  String get forgotPassword => 'पासवर्ड भूल गए?';
  @override
  String get rememberMe => 'मुझे याद रखें';
  @override
  String get dontHaveAccount => 'खाता नहीं है?';
  @override
  String get alreadyHaveAccount => 'पहले से खाता है?';
  @override
  String get createAccount => 'खाता बनाएं';
  @override
  String get or => 'या';
  @override
  String get signInWithGoogle => 'Google से साइन इन करें';
  @override
  String get signInWithFacebook => 'Facebook से साइन इन करें';

  // Spiritual Dashboard
  @override
  String get spiritualGreeting => 'ॐ नमः शिवाय';
  @override
  String get spiritualSubGreeting => 'आज अपनी आध्यात्मिक यात्रा शुरू करें';
  @override
  String get todaysMantra => 'आज का मंत्र';
  @override
  String get spiritualPractices => 'आध्यात्मिक अभ्यास';
  @override
  String get ramNaamJaap => 'राम नाम जप';
  @override
  String get meditation => 'ध्यान';
  @override
  String get prayers => 'प्रार्थना';
  @override
  String get temple => 'मंदिर';
  @override
  String get sacredTexts => 'पवित्र ग्रंथ';
  @override
  String get spiritualCalendar => 'आध्यात्मिक कैलेंडर';
  @override
  String get quickActions => 'त्वरित कार्य';
  @override
  String get startJaap => 'जप शुरू करें';
  @override
  String get playMantras => 'मंत्र चलाएं';
  @override
  String get dailyPrayer => 'दैनिक प्रार्थना';
  @override
  String get spiritualQuote => 'आध्यात्मिक उद्धरण';
  @override
  String get blessings => 'दिव्य आशीर्वाद';
  @override
  String get auspiciousTime => 'शुभ समय';
  @override
  String get festivals => 'त्योहार';
  @override
  String get deities => 'देवता';
  @override
  String get ganesh => 'गणेश';
  @override
  String get shiva => 'शिव';
  @override
  String get krishna => 'कृष्ण';
  @override
  String get hanuman => 'हनुमान';
  @override
  String get durga => 'दुर्गा';
  @override
  String get ram => 'राम';

  // Ram Naam Jaap
  @override
  String get ramNaamJaapTitle => 'राम नाम जप';
  @override
  String get ramNaamJaapSubtitle => 'Ram Naam Jaap';
  @override
  String get writeRamName => 'भगवान राम का पवित्र नाम 108 बार लिखें';
  @override
  String get startWritingRam => 'राम लिखना शुरू करें';
  @override
  String get jaapCount => 'जप गिनती';
  @override
  String get ramMantra => 'राम मंत्र';
  @override
  String get ramChalisa => 'राम चालीसा';
  @override
  String get ramTeaching => 'राम की शिक्षा';
  @override
  String get ramDevotion => 'राम भक्ति';
  @override
  String get todaysRamGuidance => 'आज का राम मार्गदर्शन';

  // Writer Screen
  @override
  String get jaap => 'जप';
  @override
  String get writeRam => 'राम';
  @override
  String get ramInHindi => 'राम';
  @override
  String get ramInEnglish => 'RAM';
  @override
  String get textColor => 'टेक्स्ट रंग';
  @override
  String get backgroundColor => 'बैकग्राउंड रंग';
  @override
  String get customizeColors => 'रंग कस्टमाइज़ करें';
  @override
  String get progress => 'प्रगति';
  @override
  String get completed => 'पूर्ण';

  // Common
  @override
  String get profile => 'प्रोफ़ाइल';
  @override
  String get settings => 'सेटिंग्स';
  @override
  String get language => 'भाषा';
  @override
  String get theme => 'थीम';
  @override
  String get darkMode => 'डार्क मोड';
  @override
  String get lightMode => 'लाइट मोड';
  @override
  String get save => 'सेव करें';
  @override
  String get cancel => 'रद्द करें';
  @override
  String get ok => 'ठीक है';
  @override
  String get yes => 'हाँ';
  @override
  String get no => 'नहीं';
  @override
  String get back => 'वापस';
  @override
  String get next => 'अगला';
  @override
  String get done => 'पूर्ण';
  @override
  String get loading => 'लोड हो रहा है...';
  @override
  String get error => 'त्रुटि';
  @override
  String get success => 'सफलता';

  // Mantras and Spiritual Content
  @override
  String get omNamahShivaya => 'ॐ नमः शिवाय';
  @override
  String get shrirRamJayRam => 'श्री राम जय राम';
  @override
  String get raghukulRiti => 'रघुकुल रीति सदा चली आई, प्राण जाय पर वचन न जाई';
  @override
  String get raghukulRitiTranslation =>
      'रघु वंश की परंपरा हमेशा से चली आई है - प्राण चले जाएं पर वचन नहीं जाना चाहिए।';
  @override
  String get brahmuMuhurta => 'ब्रह्म मुहूर्त';
  @override
  String get auspiciousTimeDesc => 'सुबह 4:00 - 6:00 बजे';
  @override
  String get buddhaQuote => 'मन ही सब कुछ है। आप जो सोचते हैं वही बन जाते हैं।';
  @override
  String get todayAuspicious => 'आज आध्यात्मिक अभ्यास के लिए शुभ दिन है';

  // Other Practices
  @override
  String get otherSpiritualPractices => 'अन्य आध्यात्मिक अभ्यास';
  @override
  String get innerPeace => 'आंतरिक शांति';
  @override
  String get dailyPrayers => 'दैनिक प्रार्थनाएं';
  @override
  String get sacredPlaces => 'पवित्र स्थान';
  @override
  String get fortyVerses => '40 श्लोक';
}
