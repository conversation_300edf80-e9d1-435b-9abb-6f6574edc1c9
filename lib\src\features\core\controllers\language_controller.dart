import 'dart:ui';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class LanguageController extends GetxController {
  static LanguageController get instance => Get.find();

  final _box = GetStorage();
  final _key = 'selectedLanguage';

  /// Reactive variable for current language
  Rx<Locale> currentLocale = const Locale('en', 'US').obs;

  /// Available languages
  final List<Language> languages = [
    Language(
      code: 'en',
      countryCode: 'US',
      name: 'English',
      nativeName: 'English',
      flag: '🇺🇸',
    ),
    Language(
      code: 'hi',
      countryCode: 'IN',
      name: 'Hindi',
      nativeName: 'हिंदी',
      flag: '🇮🇳',
    ),
  ];

  @override
  void onInit() {
    super.onInit();
    // Load saved language preference
    final savedLanguage = _box.read(_key);
    if (savedLanguage != null) {
      final language = languages.firstWhere(
        (lang) => lang.code == savedLanguage,
        orElse: () => languages.first,
      );
      currentLocale.value = Locale(language.code, language.countryCode);
    }
    // Apply the locale
    Get.updateLocale(currentLocale.value);
  }

  /// Change language
  void changeLanguage(String languageCode) {
    final language = languages.firstWhere(
      (lang) => lang.code == languageCode,
      orElse: () => languages.first,
    );
    
    currentLocale.value = Locale(language.code, language.countryCode);
    _box.write(_key, languageCode);
    Get.updateLocale(currentLocale.value);
  }

  /// Get current language
  Language get currentLanguage {
    return languages.firstWhere(
      (lang) => lang.code == currentLocale.value.languageCode,
      orElse: () => languages.first,
    );
  }

  /// Check if current language is Hindi
  bool get isHindi => currentLocale.value.languageCode == 'hi';

  /// Check if current language is English
  bool get isEnglish => currentLocale.value.languageCode == 'en';
}

class Language {
  final String code;
  final String countryCode;
  final String name;
  final String nativeName;
  final String flag;

  Language({
    required this.code,
    required this.countryCode,
    required this.name,
    required this.nativeName,
    required this.flag,
  });

  Locale get locale => Locale(code, countryCode);
}
